#!/usr/bin/env python3
"""
Debug script to test TikTok integration with Unipile API
"""

import requests
import json

def debug_tiktok_api():
    """Debug the TikTok API integration"""
    
    api_key = "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE="
    dsn = "https://api1.unipile.com:13115"
    
    print("🔍 Debugging Unipile TikTok API Integration")
    print("=" * 60)
    
    # First, check existing accounts
    print("\n1. Checking existing accounts...")
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'X-API-KEY': api_key,
            'accept': 'application/json'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        print(f"GET /accounts - Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("Existing accounts:")
            print(json.dumps(data, indent=2))
            
            if isinstance(data, dict) and 'items' in data:
                accounts = data['items']
                tiktok_accounts = [acc for acc in accounts if acc.get('type') == 'TIKTOK']
                print(f"\nFound {len(tiktok_accounts)} existing TikTok accounts")
                for i, account in enumerate(tiktok_accounts):
                    print(f"TikTok Account {i+1}: ID={account.get('id')}, Name={account.get('name')}")
                    
                # Show all platform summary
                platform_summary = {}
                for account in accounts:
                    platform = account.get('type', 'Unknown')
                    platform_summary[platform] = platform_summary.get(platform, 0) + 1
                
                print(f"\n📊 Platform Summary:")
                for platform, count in platform_summary.items():
                    status_icon = "✅" if count > 0 else "❌"
                    print(f"  {status_icon} {platform}: {count} account(s)")
                    
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error checking accounts: {e}")
    
    # Now try creating a new TikTok account (this will fail without credentials)
    print("\n2. Testing TikTok account creation (without credentials)...")
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'Content-Type': 'application/json',
            'X-API-KEY': api_key
        }
        payload = {"provider": "TIKTOK"}
        
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        print(f"POST /accounts - Status: {response.status_code}")
        
        if response.status_code in [200, 201]:
            data = response.json()
            print("Create TikTok account response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Expected error (TikTok requires credentials): {response.status_code}")
            # Don't print the full error as it's very long
            
    except Exception as e:
        print(f"Error creating TikTok account: {e}")

    print("\n3. TikTok Authentication Requirements:")
    print("TikTok requires username and password authentication:")
    print("   - username: TikTok username")
    print("   - password: TikTok password")
    print("\nExample usage:")
    print("python tiktok_client.py auth_credentials your_username your_password")
    
    print("\n4. TikTok Features Available:")
    print("✅ Username and password authentication")
    print("✅ Video posting with captions")
    print("✅ Direct messaging")
    print("✅ Profile information access")
    print("✅ Followers list and analytics")
    print("✅ Video management and statistics")
    print("✅ Account status monitoring")
    
    print("\n5. TikTok vs Other Platforms:")
    print("🎵 TikTok: Username/Password → Videos, DMs, Viral content")
    print("📘 Facebook: Email/Password → Posts, Pages, Messenger")
    print("📱 Telegram: QR Code → Channels, Bots, Groups")
    print("📱 Instagram: Username/Password → Visual content, DMs")
    print("💼 LinkedIn: Email/Password → Professional posts")
    print("📞 WhatsApp: QR Code → Personal/Business messaging")
    
    print("\n6. Common TikTok Use Cases:")
    print("🎯 Viral video content creation")
    print("🎯 Short-form video marketing")
    print("🎯 Influencer content automation")
    print("🎯 Brand awareness campaigns")
    print("🎯 Music and dance trend participation")
    print("🎯 Educational content (EduTok)")
    print("🎯 Product demonstrations and reviews")
    print("🎯 Community engagement and challenges")
    
    print("\n7. TikTok Authentication Flow:")
    print("Step 1: 📱 Enter username and password")
    print("Step 2: ✅ Account authenticated (may require verification)")
    print("Step 3: 🎬 Upload videos and engage with community")
    
    print("\n8. Platform Comparison Summary:")
    platforms = [
        {"name": "TikTok", "auth": "Username/Password", "strength": "Viral Videos"},
        {"name": "Facebook", "auth": "Email/Password", "strength": "Posts & Pages"},
        {"name": "Telegram", "auth": "QR Code", "strength": "Channels & Bots"},
        {"name": "Instagram", "auth": "Username/Password", "strength": "Visual Content"},
        {"name": "LinkedIn", "auth": "Email/Password", "strength": "Professional"},
        {"name": "WhatsApp", "auth": "QR Code", "strength": "Personal Messaging"}
    ]
    
    print("┌─────────────┬─────────────────┬─────────────────────┐")
    print("│ Platform    │ Authentication  │ Primary Strength    │")
    print("├─────────────┼─────────────────┼─────────────────────┤")
    for platform in platforms:
        print(f"│ {platform['name']:<11} │ {platform['auth']:<15} │ {platform['strength']:<19} │")
    print("└─────────────┴─────────────────┴─────────────────────┘")
    
    print("\n9. TikTok Unique Features:")
    print("🎵 Short-form vertical videos (15s-10min)")
    print("🎶 Music integration and sound effects")
    print("🔥 Trending hashtags and challenges")
    print("🤖 AI-powered For You Page algorithm")
    print("🎭 Creative filters and effects")
    print("📊 Advanced analytics for creators")
    print("💰 Creator monetization programs")
    print("🛍️ TikTok Shop integration")
    
    print("\n10. TikTok Demographics & Reach:")
    print("👥 1+ billion active users worldwide")
    print("🎯 Primary age group: 16-34 years old")
    print("🌍 Available in 150+ countries")
    print("⏱️ Average session time: 52 minutes")
    print("📱 Mobile-first platform (99% mobile usage)")
    print("🚀 Fastest-growing social media platform")
    
    print("\n11. Integration Benefits:")
    print("✅ Access to Gen Z and millennial audiences")
    print("✅ Viral content potential and organic reach")
    print("✅ Creative video editing and effects")
    print("✅ Music and sound integration")
    print("✅ Trend participation and hashtag challenges")
    print("✅ Influencer marketing opportunities")
    print("✅ E-commerce and shopping features")

if __name__ == "__main__":
    debug_tiktok_api()
