#!/usr/bin/env python3
"""
Debug script to test Telegram integration with Unipile API
"""

import requests
import json

def debug_telegram_api():
    """Debug the Telegram API integration"""
    
    api_key = "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE="
    dsn = "https://api1.unipile.com:13115"
    
    print("🔍 Debugging Unipile Telegram API Integration")
    print("=" * 60)
    
    # First, check existing accounts
    print("\n1. Checking existing accounts...")
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'X-API-KEY': api_key,
            'accept': 'application/json'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        print(f"GET /accounts - Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("Existing accounts:")
            print(json.dumps(data, indent=2))
            
            if isinstance(data, dict) and 'items' in data:
                accounts = data['items']
                telegram_accounts = [acc for acc in accounts if acc.get('type') == 'TELEGRAM']
                print(f"\nFound {len(telegram_accounts)} existing Telegram accounts")
                for i, account in enumerate(telegram_accounts):
                    print(f"Telegram Account {i+1}: ID={account.get('id')}, Name={account.get('name')}")
                    
                # Show all platform summary
                platform_summary = {}
                for account in accounts:
                    platform = account.get('type', 'Unknown')
                    platform_summary[platform] = platform_summary.get(platform, 0) + 1
                
                print(f"\n📊 Platform Summary:")
                for platform, count in platform_summary.items():
                    status_icon = "✅" if count > 0 else "❌"
                    print(f"  {status_icon} {platform}: {count} account(s)")
                    
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error checking accounts: {e}")
    
    # Now try creating a new Telegram account (this will fail without phone number)
    print("\n2. Testing Telegram account creation (without phone number)...")
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'Content-Type': 'application/json',
            'X-API-KEY': api_key
        }
        payload = {"provider": "TELEGRAM"}
        
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        print(f"POST /accounts - Status: {response.status_code}")
        
        if response.status_code in [200, 201]:
            data = response.json()
            print("Create Telegram account response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Expected error (Telegram requires phone number): {response.status_code}")
            # Don't print the full error as it's very long
            
    except Exception as e:
        print(f"Error creating Telegram account: {e}")

    print("\n3. Telegram Authentication Requirements:")
    print("Telegram requires phone number verification:")
    print("   - phone_number: Phone number with country code (+**********)")
    print("   - verification_code: SMS code received on phone")
    print("\nExample usage:")
    print("python telegram_client.py auth_phone +**********")
    print("python telegram_client.py verify_code 123456 account_id")
    
    print("\n4. Telegram Features Available:")
    print("✅ Phone number authentication")
    print("✅ SMS verification")
    print("✅ Direct messaging")
    print("✅ Channel broadcasting")
    print("✅ Group messaging")
    print("✅ Profile information")
    print("✅ Chat management")
    print("✅ Bot interactions")
    
    print("\n5. Telegram vs Other Platforms:")
    print("📱 Telegram: Phone + SMS → Messaging & Channels")
    print("📱 Instagram: Username/Password → Visual content & DMs")
    print("💼 LinkedIn: Username/Password → Professional posts")
    print("📞 WhatsApp: QR Code → Personal/Business messaging")
    
    print("\n6. Common Telegram Use Cases:")
    print("🎯 Automated channel broadcasting")
    print("🎯 Bot development and management")
    print("🎯 Group administration")
    print("🎯 Customer support automation")
    print("🎯 News and content distribution")
    print("🎯 Community management")
    
    print("\n7. Telegram Authentication Flow:")
    print("Step 1: 📱 Send phone number → Receive SMS")
    print("Step 2: ✅ Enter SMS code → Account verified")
    print("Step 3: 🚀 Use messaging features")
    
    print("\n8. Platform Comparison Summary:")
    platforms = [
        {"name": "Telegram", "auth": "Phone + SMS", "strength": "Channels & Bots"},
        {"name": "Instagram", "auth": "Username/Password", "strength": "Visual Content"},
        {"name": "LinkedIn", "auth": "Email/Password", "strength": "Professional"},
        {"name": "WhatsApp", "auth": "QR Code", "strength": "Personal Messaging"}
    ]
    
    print("┌─────────────┬─────────────────┬─────────────────────┐")
    print("│ Platform    │ Authentication  │ Primary Strength    │")
    print("├─────────────┼─────────────────┼─────────────────────┤")
    for platform in platforms:
        print(f"│ {platform['name']:<11} │ {platform['auth']:<15} │ {platform['strength']:<19} │")
    print("└─────────────┴─────────────────┴─────────────────────┘")

if __name__ == "__main__":
    debug_telegram_api()
