#!/usr/bin/env python3
"""
Test script to verify Unipile API connection
"""

import os
import requests
import json

def test_api_connection():
    """Test the Unipile API connection"""
    
    # API credentials from your curl command
    api_key = "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE="
    dsn = "https://api1.unipile.com:13115"
    
    print("Testing Unipile API Connection...")
    print(f"API Key: {api_key[:20]}...")
    print(f"DSN: {dsn}")
    print("-" * 50)
    
    try:
        # Test GET /accounts endpoint
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'X-API-KEY': api_key,
            'accept': 'application/json'
        }
        
        print(f"Making request to: {url}")
        response = requests.get(url, headers=headers, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ API Connection Successful!")
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
            
            if isinstance(data, list):
                print(f"Found {len(data)} existing accounts")
            
        else:
            print("❌ API Connection Failed!")
            print(f"Error: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection Error: {e}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON Decode Error: {e}")
        print(f"Raw response: {response.text}")
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")

def test_whatsapp_account_creation():
    """Test creating a WhatsApp account"""
    
    api_key = "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE="
    dsn = "https://api1.unipile.com:13115"
    
    print("\nTesting WhatsApp Account Creation...")
    print("-" * 50)
    
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'Content-Type': 'application/json',
            'X-API-KEY': api_key
        }
        payload = {"provider": "WHATSAPP"}
        
        print(f"Creating WhatsApp account...")
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code in [200, 201]:
            print("✅ WhatsApp Account Creation Successful!")
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
            
            if 'qrCodeString' in data:
                print(f"✅ QR Code String received: {data['qrCodeString'][:50]}...")
            else:
                print("⚠️ No QR Code String in response")
                
        else:
            print("❌ WhatsApp Account Creation Failed!")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    # Set environment variables for testing
    os.environ['UNIPILE_API_KEY'] = "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE="
    os.environ['UNIPILE_DSN'] = "https://api1.unipile.com:13115"
    
    test_api_connection()
    test_whatsapp_account_creation()
