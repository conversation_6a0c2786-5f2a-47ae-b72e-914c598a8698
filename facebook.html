<!DOCTYPE html>
<html>
<head>
  <title>Connect Facebook with Unipile</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 50px auto;
      padding: 20px;
      background: linear-gradient(135deg, #4267B2 0%, #365899 50%, #1e3a8a 100%);
      min-height: 100vh;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 20px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.15);
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .facebook-logo {
      color: #4267B2;
      font-size: 32px;
      font-weight: bold;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    button {
      background: linear-gradient(135deg, #4267B2 0%, #365899 100%);
      color: white;
      border: none;
      padding: 14px 28px;
      font-size: 16px;
      border-radius: 30px;
      cursor: pointer;
      margin: 10px 5px;
      min-width: 160px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(66,103,178,0.3);
    }
    button:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(66,103,178,0.4);
    }
    button:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    .error {
      color: #e74c3c;
      margin-top: 10px;
      padding: 15px;
      background-color: #fdf2f2;
      border-radius: 12px;
      border-left: 5px solid #e74c3c;
    }
    .success {
      color: #27ae60;
      margin-top: 10px;
      padding: 15px;
      background-color: #f0f9f0;
      border-radius: 12px;
      border-left: 5px solid #27ae60;
    }
    .warning {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
      padding: 20px;
      border-radius: 15px;
      margin-bottom: 25px;
      box-shadow: 0 4px 15px rgba(243,156,18,0.3);
    }
    .config-section {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: 20px;
      border-radius: 15px;
      margin-bottom: 25px;
      border: 2px solid #dee2e6;
    }
    input[type="text"], input[type="email"], input[type="password"], textarea {
      width: 100%;
      padding: 15px;
      margin: 10px 0;
      border: 2px solid #e1e8ed;
      border-radius: 12px;
      box-sizing: border-box;
      transition: all 0.3s ease;
      font-size: 16px;
    }
    input[type="text"]:focus, input[type="email"]:focus, input[type="password"]:focus, textarea:focus {
      border-color: #4267B2;
      outline: none;
      box-shadow: 0 0 0 3px rgba(66,103,178,0.1);
    }
    textarea {
      height: 120px;
      resize: vertical;
    }
    .section {
      margin-bottom: 30px;
      padding: 25px;
      border: 2px solid #e0e6ed;
      border-radius: 20px;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    }
    .section h3 {
      margin-top: 0;
      color: #4267B2;
      font-size: 24px;
    }
    .account-info {
      background: linear-gradient(135deg, #4267B2 0%, #365899 100%);
      color: white;
      padding: 25px;
      border-radius: 15px;
      margin: 20px 0;
      box-shadow: 0 6px 25px rgba(66,103,178,0.3);
    }
    .button-group {
      text-align: center;
      margin: 25px 0;
    }
    .feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin: 25px 0;
    }
    .feature-card {
      background: white;
      padding: 25px;
      border-radius: 15px;
      border: 2px solid #e1e8ed;
      text-align: center;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    }
    .feature-card:hover {
      border-color: #4267B2;
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(66,103,178,0.15);
    }
    .step-indicator {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1><span class="facebook-logo">📘 Facebook</span> Integration with Unipile</h1>
      <p>Connect your Facebook account for automated posting, messaging, and page management</p>
    </div>
    
    <div class="warning">
      <strong>⚠️ Security Notice:</strong> Never expose your API key in client-side code. 
      This is for development/testing only. In production, use a backend server to handle API calls.
    </div>

    <div class="config-section">
      <h3>🔧 Configuration</h3>
      <label for="apiKey">API Key:</label>
      <input type="text" id="apiKey" value="zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE=" placeholder="Enter your Unipile API key" />
      
      <label for="dsn">DSN:</label>
      <input type="text" id="dsn" value="https://api1.unipile.com:13115" placeholder="Enter your DSN" />
    </div>

    <!-- Authentication Section -->
    <div class="section">
      <h3>1. 🔐 Connect Facebook Account</h3>
      <p>Enter your Facebook credentials to connect your account:</p>
      
      <div style="margin-bottom: 20px;">
        <label for="username">Facebook Email/Username:</label>
        <input type="email" id="username" placeholder="Enter your Facebook email or username" />
        
        <label for="password">Facebook Password:</label>
        <input type="password" id="password" placeholder="Enter your Facebook password" />
        
        <div class="button-group">
          <button onclick="authenticateWithCredentials()">🔗 Connect Facebook Account</button>
        </div>
      </div>
      
      <div id="authContainer"></div>
    </div>

    <!-- Account Status Section -->
    <div class="section">
      <h3>2. 📊 Check Account Status</h3>
      <p>Verify your Facebook account connection and status.</p>
      <div class="button-group">
        <button onclick="checkAccountStatus()">📋 Check Status</button>
      </div>
      <div id="statusContainer"></div>
    </div>

    <!-- Features Grid -->
    <div class="section">
      <h3>3. 🚀 Facebook Features</h3>
      <div class="feature-grid">
        
        <!-- Post Content -->
        <div class="feature-card">
          <h4>📝 Post Content</h4>
          <label for="postText">Post Text:</label>
          <textarea id="postText" placeholder="What's on your mind?

Share your thoughts with the world! 🌍
#facebook #automation #unipile"></textarea>
          <label for="postImageUrl">Image URL (optional):</label>
          <input type="text" id="postImageUrl" placeholder="https://example.com/image.jpg" />
          <button onclick="postContent()">📘 Post to Facebook</button>
          <div id="postContainer"></div>
        </div>

        <!-- Send Messages -->
        <div class="feature-card">
          <h4>💬 Messenger</h4>
          <label for="messageRecipient">To (username or ID):</label>
          <input type="text" id="messageRecipient" placeholder="friend.username or user_id" />
          <label for="messageText">Message:</label>
          <textarea id="messageText" placeholder="Enter your message...

Hello from Facebook automation! 🤖"></textarea>
          <button onclick="sendMessage()">📤 Send Message</button>
          <div id="messageContainer"></div>
        </div>

        <!-- Profile Info -->
        <div class="feature-card">
          <h4>👤 Profile Info</h4>
          <p>Get your Facebook profile information and statistics.</p>
          <button onclick="getProfile()">📊 Get Profile</button>
          <div id="profileContainer"></div>
        </div>

        <!-- Friends -->
        <div class="feature-card">
          <h4>👥 Friends</h4>
          <p>Retrieve your friends list and social connections.</p>
          <button onclick="getFriends()">🤝 Get Friends</button>
          <div id="friendsContainer"></div>
        </div>

        <!-- Pages -->
        <div class="feature-card">
          <h4>📄 Pages</h4>
          <p>Manage Facebook pages you own or administer.</p>
          <button onclick="getPages()">📋 Get Pages</button>
          <div id="pagesContainer"></div>
        </div>

      </div>
    </div>

    <div id="message"></div>
  </div>

  <script>
    let currentAccountId = null;

    async function makeAPICall(endpoint, method = 'GET', body = null, additionalHeaders = {}) {
      const apiKey = document.getElementById('apiKey').value.trim();
      const dsn = document.getElementById('dsn').value.trim();
      
      if (!apiKey || !dsn) {
        throw new Error('Please enter both API key and DSN');
      }

      const headers = {
        'X-API-KEY': apiKey,
        'accept': 'application/json',
        ...additionalHeaders
      };

      if (method !== 'GET') {
        headers['Content-Type'] = 'application/json';
      }

      const config = {
        method: method,
        headers: headers
      };

      if (body) {
        config.body = JSON.stringify(body);
      }

      const response = await fetch(`${dsn}${endpoint}`, config);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return await response.json();
    }

    async function authenticateWithCredentials() {
      const container = document.getElementById('authContainer');
      const username = document.getElementById('username').value.trim();
      const password = document.getElementById('password').value.trim();
      
      try {
        if (!username || !password) {
          throw new Error('Please enter both username and password');
        }

        container.innerHTML = '<p>🔄 Authenticating with Facebook...</p>';

        const data = await makeAPICall('/api/v1/accounts', 'POST', {
          provider: 'FACEBOOK',
          username: username,
          password: password
        });
        
        console.log('Auth Response:', data);
        currentAccountId = data.account_id;

        if (data.object === 'Account') {
          container.innerHTML = `
            <div class="account-info">
              <h4>✅ Facebook Authentication Successful!</h4>
              <p><strong>Account ID:</strong> ${currentAccountId}</p>
              <p><strong>Username:</strong> ${username}</p>
              <p><strong>Status:</strong> Connected</p>
              <details style="margin-top: 10px;">
                <summary>Full Response Data</summary>
                <pre style="font-size: 12px;">${JSON.stringify(data, null, 2)}</pre>
              </details>
            </div>
          `;
          showMessage('Facebook account connected successfully!', 'success');
        } else if (data.object === 'Checkpoint') {
          const checkpointType = data.checkpoint?.type || 'Unknown';
          container.innerHTML = `
            <div class="account-info" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
              <h4>⚠️ Facebook Checkpoint Required</h4>
              <p><strong>Type:</strong> ${checkpointType}</p>
              <p><strong>Account ID:</strong> ${data.account_id}</p>
              ${checkpointType === 'CAPTCHA' ? `
                <p>CAPTCHA verification required. Contact Unipile support for assistance.</p>
              ` : checkpointType === '2FA' ? `
                <p>Two-factor authentication required. Check your phone/email for verification code.</p>
              ` : `
                <p>Additional verification required: ${checkpointType}</p>
              `}
            </div>
          `;
          showMessage(`Facebook requires ${checkpointType} verification`, 'error');
        }
        
        // Clear password field for security
        document.getElementById('password').value = '';

      } catch (error) {
        console.error('Auth Error:', error);
        showMessage(`Failed to authenticate: ${error.message}`, 'error');
        container.innerHTML = `
          <div class="error">
            <h4>❌ Authentication Failed</h4>
            <p>${error.message}</p>
          </div>
        `;
      }
    }

    async function checkAccountStatus() {
      const container = document.getElementById('statusContainer');
      
      try {
        if (!currentAccountId) {
          throw new Error('No account ID available. Please authenticate first.');
        }

        container.innerHTML = '<p>🔄 Checking account status...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}`);
        console.log('Status Response:', data);

        if (data.object === 'Account') {
          const sources = data.sources || [];
          const status = sources.length > 0 ? sources[0].status : 'Unknown';
          
          container.innerHTML = `
            <div class="account-info">
              <h4>✅ Account Status: ${status}</h4>
              <p><strong>Type:</strong> ${data.type}</p>
              <p><strong>Name:</strong> ${data.name}</p>
              <p><strong>ID:</strong> ${data.id}</p>
              <p><strong>Created:</strong> ${new Date(data.created_at).toLocaleString()}</p>
              <p><strong>Sources:</strong> ${sources.length} available</p>
            </div>
          `;
          showMessage(`Account status: ${status}`, status === 'OK' ? 'success' : 'error');
        } else {
          container.innerHTML = `
            <div class="account-info">
              <h4>Account Information</h4>
              <pre>${JSON.stringify(data, null, 2)}</pre>
            </div>
          `;
        }

      } catch (error) {
        console.error('Status Error:', error);
        if (error.message.includes('404')) {
          container.innerHTML = `
            <div class="error">
              <h4>❌ Account Not Found</h4>
              <p>Please authenticate again.</p>
            </div>
          `;
        } else {
          container.innerHTML = `
            <div class="error">
              <h4>❌ Error Checking Status</h4>
              <p>${error.message}</p>
            </div>
          `;
        }
        showMessage(`Failed to check status: ${error.message}`, 'error');
      }
    }

    async function postContent() {
      const container = document.getElementById('postContainer');
      const text = document.getElementById('postText').value.trim();
      const imageUrl = document.getElementById('postImageUrl').value.trim();
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }
        if (!text) {
          throw new Error('Please enter post text.');
        }

        container.innerHTML = '<p>📘 Posting to Facebook...</p>';

        const payload = { text: text };
        if (imageUrl) {
          payload.image_url = imageUrl;
        }

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/posts`, 'POST', payload);
        
        container.innerHTML = `
          <div class="success">
            <h5>✅ Posted Successfully!</h5>
            <p><strong>Text:</strong> "${text}"</p>
            ${imageUrl ? `<p><strong>Image:</strong> ${imageUrl}</p>` : ''}
          </div>
        `;
        
        showMessage('Content posted to Facebook successfully!', 'success');
        document.getElementById('postText').value = '';
        document.getElementById('postImageUrl').value = '';

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Post</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to post content: ${error.message}`, 'error');
      }
    }

    async function sendMessage() {
      const container = document.getElementById('messageContainer');
      const recipient = document.getElementById('messageRecipient').value.trim();
      const message = document.getElementById('messageText').value.trim();
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }
        if (!recipient || !message) {
          throw new Error('Please enter both recipient and message.');
        }

        container.innerHTML = '<p>📤 Sending message...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/messages`, 'POST', {
          to: recipient,
          text: message
        });
        
        container.innerHTML = `
          <div class="success">
            <h5>✅ Message Sent!</h5>
            <p><strong>To:</strong> ${recipient}</p>
            <p><strong>Message:</strong> "${message}"</p>
          </div>
        `;
        
        showMessage('Message sent successfully!', 'success');
        document.getElementById('messageText').value = '';

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Send</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to send message: ${error.message}`, 'error');
      }
    }

    async function getProfile() {
      const container = document.getElementById('profileContainer');
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }

        container.innerHTML = '<p>👤 Getting profile info...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/profile`);
        
        container.innerHTML = `
          <div class="success">
            <h5>✅ Profile Retrieved</h5>
            <p><strong>Name:</strong> ${data.name || 'N/A'}</p>
            <p><strong>Email:</strong> ${data.email || 'N/A'}</p>
            <p><strong>Friends:</strong> ${data.friends_count || 'N/A'}</p>
            <p><strong>ID:</strong> ${data.id || 'N/A'}</p>
          </div>
        `;
        
        showMessage('Profile information retrieved!', 'success');

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Get Profile</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to get profile: ${error.message}`, 'error');
      }
    }

    async function getFriends() {
      const container = document.getElementById('friendsContainer');
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }

        container.innerHTML = '<p>👥 Getting friends...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/friends`);
        
        const friends = data.friends || data.items || [];
        container.innerHTML = `
          <div class="success">
            <h5>✅ Friends Retrieved</h5>
            <p><strong>Count:</strong> ${friends.length}</p>
            ${friends.length > 0 ? `<p>Friends list loaded</p>` : '<p>No friends data available</p>'}
          </div>
        `;
        
        showMessage('Friends information retrieved!', 'success');

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Get Friends</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to get friends: ${error.message}`, 'error');
      }
    }

    async function getPages() {
      const container = document.getElementById('pagesContainer');
      
      try {
        if (!currentAccountId) {
          throw new Error('Please authenticate first.');
        }

        container.innerHTML = '<p>📄 Getting pages...</p>';

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/pages`);
        
        const pages = data.pages || data.items || [];
        container.innerHTML = `
          <div class="success">
            <h5>✅ Pages Retrieved</h5>
            <p><strong>Count:</strong> ${pages.length}</p>
            ${pages.length > 0 ? `<p>Pages list loaded</p>` : '<p>No pages data available</p>'}
          </div>
        `;
        
        showMessage('Pages information retrieved!', 'success');

      } catch (error) {
        container.innerHTML = `
          <div class="error">
            <h5>❌ Failed to Get Pages</h5>
            <p>${error.message}</p>
          </div>
        `;
        showMessage(`Failed to get pages: ${error.message}`, 'error');
      }
    }

    function showMessage(text, type) {
      const messageDiv = document.getElementById('message');
      messageDiv.textContent = text;
      messageDiv.className = type;
      
      // Auto-clear message after 5 seconds
      setTimeout(() => {
        messageDiv.textContent = '';
        messageDiv.className = '';
      }, 5000);
    }

    // Auto-focus on username input
    document.addEventListener('DOMContentLoaded', function() {
      document.getElementById('username').focus();
    });
  </script>
</body>
</html>
