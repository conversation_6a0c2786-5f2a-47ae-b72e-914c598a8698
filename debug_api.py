#!/usr/bin/env python3
"""
Debug script to examine the exact API response structure
"""

import os
import requests
import json

def debug_api_response():
    """Debug the API response to understand the structure"""
    
    api_key = "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE="
    dsn = "https://api1.unipile.com:13115"
    
    print("🔍 Debugging Unipile API Response Structure")
    print("=" * 60)
    
    # First, check existing accounts
    print("\n1. Checking existing accounts...")
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'X-API-KEY': api_key,
            'accept': 'application/json'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        print(f"GET /accounts - Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("Existing accounts:")
            print(json.dumps(data, indent=2))
            
            if isinstance(data, dict) and 'items' in data:
                accounts = data['items']
                print(f"\nFound {len(accounts)} existing accounts")
                for i, account in enumerate(accounts):
                    print(f"Account {i+1}: ID={account.get('id')}, Type={account.get('type')}, Name={account.get('name')}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error checking accounts: {e}")
    
    # Now try creating a new WhatsApp account
    print("\n2. Creating new WhatsApp account...")
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'Content-Type': 'application/json',
            'X-API-KEY': api_key
        }
        payload = {"provider": "WHATSAPP"}
        
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        print(f"POST /accounts - Status: {response.status_code}")
        
        if response.status_code in [200, 201]:
            data = response.json()
            print("Create account response:")
            print(json.dumps(data, indent=2))
            
            # Analyze the structure
            print("\n🔍 Response Analysis:")
            print(f"- Object type: {data.get('object', 'N/A')}")
            print(f"- Account ID: {data.get('account_id', 'N/A')}")
            
            # Check for QR code in various locations
            qr_locations = []
            
            if 'qrCodeString' in data:
                qr_locations.append(f"data.qrCodeString = {data['qrCodeString'][:50]}...")
            
            if 'checkpoint' in data:
                checkpoint = data['checkpoint']
                print(f"- Checkpoint type: {checkpoint.get('type', 'N/A')}")
                if 'qrcode' in checkpoint:
                    qr_locations.append(f"data.checkpoint.qrcode = {checkpoint['qrcode'][:50]}...")
                if 'qrCodeString' in checkpoint:
                    qr_locations.append(f"data.checkpoint.qrCodeString = {checkpoint['qrCodeString'][:50]}...")
            
            if 'qrcode' in data:
                qr_locations.append(f"data.qrcode = {data['qrcode'][:50]}...")
                
            if 'qr_code' in data:
                qr_locations.append(f"data.qr_code = {data['qr_code'][:50]}...")
            
            print(f"- QR Code locations found: {len(qr_locations)}")
            for location in qr_locations:
                print(f"  ✅ {location}")
                
            if not qr_locations:
                print("  ❌ No QR code found in any expected location!")
                print("  📋 Available keys in response:")
                for key in data.keys():
                    print(f"    - {key}: {type(data[key])}")
                    
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error creating account: {e}")

if __name__ == "__main__":
    debug_api_response()
