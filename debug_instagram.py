#!/usr/bin/env python3
"""
Debug script to test Instagram integration with Unipile API
"""

import requests
import json

def debug_instagram_api():
    """Debug the Instagram API integration"""
    
    api_key = "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE="
    dsn = "https://api1.unipile.com:13115"
    
    print("🔍 Debugging Unipile Instagram API Integration")
    print("=" * 60)
    
    # First, check existing accounts
    print("\n1. Checking existing accounts...")
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'X-API-KEY': api_key,
            'accept': 'application/json'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        print(f"GET /accounts - Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("Existing accounts:")
            print(json.dumps(data, indent=2))
            
            if isinstance(data, dict) and 'items' in data:
                accounts = data['items']
                instagram_accounts = [acc for acc in accounts if acc.get('type') == 'INSTAGRAM']
                print(f"\nFound {len(instagram_accounts)} existing Instagram accounts")
                for i, account in enumerate(instagram_accounts):
                    print(f"Instagram Account {i+1}: ID={account.get('id')}, Name={account.get('name')}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error checking accounts: {e}")
    
    # Now try creating a new Instagram account (this will fail without credentials)
    print("\n2. Testing Instagram account creation (without credentials)...")
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'Content-Type': 'application/json',
            'X-API-KEY': api_key
        }
        payload = {"provider": "INSTAGRAM"}
        
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        print(f"POST /accounts - Status: {response.status_code}")
        
        if response.status_code in [200, 201]:
            data = response.json()
            print("Create Instagram account response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Expected error (Instagram requires credentials): {response.status_code}")
            # Don't print the full error as it's very long
            
    except Exception as e:
        print(f"Error creating Instagram account: {e}")

    print("\n3. Instagram Authentication Requirements:")
    print("Instagram requires username and password authentication:")
    print("   - username: Instagram username")
    print("   - password: Instagram password")
    print("\nExample usage:")
    print("python instagram_client.py auth_credentials your_username your_password")
    
    print("\n4. Instagram Features Available:")
    print("✅ Account authentication")
    print("✅ Direct messaging")
    print("✅ Content posting (with images)")
    print("✅ Profile information")
    print("✅ Followers list")
    print("✅ Account status monitoring")
    
    print("\n5. Instagram vs Other Platforms:")
    print("📱 Instagram: Username/Password → Direct posting & messaging")
    print("📞 WhatsApp: QR Code → Messaging only")
    print("💼 LinkedIn: Username/Password → Professional posting")
    
    print("\n6. Common Instagram Use Cases:")
    print("🎯 Automated posting with captions and images")
    print("🎯 Direct message automation")
    print("🎯 Follower analytics and management")
    print("🎯 Profile monitoring and updates")
    print("🎯 Content scheduling and management")

if __name__ == "__main__":
    debug_instagram_api()
