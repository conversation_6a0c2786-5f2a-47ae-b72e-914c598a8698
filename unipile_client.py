#!/usr/bin/env python3
"""
Unipile WhatsApp Integration Script

This script provides a Python interface for connecting WhatsApp accounts
using the Unipile API. It includes functions for generating QR codes,
checking connection status, and sending messages.

Security Note: Store your API key in environment variables or a secure config file.
Never hardcode API keys in your source code.
"""

import os
import sys
import json
import requests
from typing import Optional, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class UnipileWhatsApp:
    """
    A Python client for Unipile WhatsApp API integration.
    """
    
    def __init__(self, api_key: Optional[str] = None, dsn: Optional[str] = None):
        """
        Initialize the Unipile WhatsApp client.
        
        Args:
            api_key: Unipile API key (if not provided, will look for UNIPILE_API_KEY env var)
            dsn: Unipile DSN endpoint (if not provided, will look for UNIPILE_DSN env var)
        """
        self.api_key = api_key or os.getenv('UNIPILE_API_KEY')
        self.dsn = dsn or os.getenv('UNIPILE_DSN', 'https://api13.unipile.com:14391')
        
        if not self.api_key:
            raise ValueError("API key is required. Set UNIPILE_API_KEY environment variable or pass api_key parameter.")
        
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'X-API-KEY': self.api_key
        })
        
        self.account_id = None
        
    def create_whatsapp_account(self) -> Dict[str, Any]:
        """
        Create a new WhatsApp account session and get QR code.
        
        Returns:
            Dict containing account information and QR code string
        """
        try:
            url = f"{self.dsn}/api/v1/accounts"
            payload = {"provider": "WHATSAPP"}
            
            logger.info("Creating WhatsApp account session...")
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            data = response.json()
            self.account_id = data.get('account_id')
            
            logger.info(f"WhatsApp account created successfully. Account ID: {self.account_id}")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to create WhatsApp account: {e}")
            raise
    
    def generate_qr_code(self, save_path: Optional[str] = None) -> str:
        """
        Generate QR code for WhatsApp connection.
        
        Args:
            save_path: Optional path to save QR code image (requires qrcode package)
            
        Returns:
            QR code string
        """
        account_data = self.create_whatsapp_account()
        qr_string = account_data.get('qrCodeString')
        
        if not qr_string:
            raise ValueError("No QR code string received from API")
        
        if save_path:
            try:
                import qrcode
                # Generate QR code image
                qr = qrcode.QRCode(version=1, box_size=10, border=5)
                qr.add_data(qr_string)
                qr.make(fit=True)
                
                img = qr.make_image(fill_color="black", back_color="white")
                img.save(save_path)
                logger.info(f"QR code saved to: {save_path}")
            except ImportError:
                logger.warning("qrcode package not installed. Install with: pip install qrcode[pil]")
        
        return qr_string
    
    def check_account_status(self, account_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Check the status of a WhatsApp account.
        
        Args:
            account_id: Account ID to check (uses self.account_id if not provided)
            
        Returns:
            Dict containing account status information
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError("No account ID available. Create an account first.")
        
        try:
            url = f"{self.dsn}/api/v1/accounts/{account_id}"
            response = self.session.get(url)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to check account status: {e}")
            raise
    
    def send_message(self, to: str, message: str, account_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Send a WhatsApp message.
        
        Args:
            to: Recipient phone number (with country code)
            message: Message text to send
            account_id: Account ID to use (uses self.account_id if not provided)
            
        Returns:
            Dict containing message send response
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError("No account ID available. Create an account first.")
        
        try:
            url = f"{self.dsn}/api/v1/accounts/{account_id}/messages"
            payload = {
                "to": to,
                "text": message
            }
            
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            logger.info(f"Message sent successfully to {to}")
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to send message: {e}")
            raise


def main():
    """
    Main function for command-line usage.
    """
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python unipile_client.py generate_qr [save_path]")
        print("  python unipile_client.py check_status [account_id]")
        print("  python unipile_client.py send_message <to> <message> [account_id]")
        print("\nEnvironment variables:")
        print("  UNIPILE_API_KEY - Your Unipile API key")
        print("  UNIPILE_DSN - Your Unipile DSN (optional)")
        sys.exit(1)
    
    command = sys.argv[1]
    
    try:
        client = UnipileWhatsApp()
        
        if command == "generate_qr":
            save_path = sys.argv[2] if len(sys.argv) > 2 else "whatsapp_qr.png"
            qr_string = client.generate_qr_code(save_path)
            print(f"QR Code generated!")
            print(f"QR String: {qr_string}")
            if save_path:
                print(f"Image saved to: {save_path}")
            print("Scan the QR code with WhatsApp to connect your account.")
            
        elif command == "check_status":
            account_id = sys.argv[2] if len(sys.argv) > 2 else None
            status = client.check_account_status(account_id)
            print(f"Account Status: {json.dumps(status, indent=2)}")
            
        elif command == "send_message":
            if len(sys.argv) < 4:
                print("Error: send_message requires <to> and <message> arguments")
                sys.exit(1)
            
            to = sys.argv[2]
            message = sys.argv[3]
            account_id = sys.argv[4] if len(sys.argv) > 4 else None
            
            result = client.send_message(to, message, account_id)
            print(f"Message sent: {json.dumps(result, indent=2)}")
            
        else:
            print(f"Unknown command: {command}")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
