# Unipile WhatsApp Integration

This repository contains both HTML and Python implementations for integrating WhatsApp with the Unipile API.

## ✅ Environment Status
- **API Connection**: ✅ Verified and Working
- **WhatsApp Account Creation**: ✅ Tested Successfully
- **QR Code Generation**: ✅ Working (Python & HTML)
- **Configuration**: ✅ Complete

## Files

### **WhatsApp Integration**
- `unipile.html` - Web-based interface for WhatsApp QR code generation
- `unipile_client.py` - Python script for WhatsApp integration
- `debug_api.py` - WhatsApp API debug script

### **LinkedIn Integration**
- `linkedin.html` - Web-based interface for LinkedIn integration
- `linkedin_client.py` - Python script for LinkedIn operations
- `debug_linkedin.py` - LinkedIn API debug script
- `LINKEDIN_README.md` - LinkedIn-specific documentation

### **Instagram Integration**
- `instagram.html` - Web-based interface for Instagram integration
- `instagram_client.py` - Python script for Instagram operations
- `debug_instagram.py` - Instagram API debug script
- `INSTAGRAM_README.md` - Instagram-specific documentation

### **Telegram Integration**
- `telegram.html` - Web-based interface for Telegram integration
- `telegram_client.py` - Python script for Telegram operations
- `debug_telegram.py` - Telegram API debug script
- `TELEGRAM_README.md` - Telegram-specific documentation

### **Facebook Integration**
- `facebook.html` - Web-based interface for Facebook integration
- `facebook_client.py` - Python script for Facebook operations
- `debug_facebook.py` - Facebook API debug script
- `FACEBOOK_README.md` - Facebook-specific documentation

### **Configuration & Setup**
- `requirements.txt` - Python dependencies
- `config.json` - Configuration file with API credentials
- `setup_environment.ps1` - PowerShell setup script
- `setup_environment.bat` - Batch setup script
- `test_connection.py` - API connection test script

## 🚀 **Quick Start**

### **WhatsApp Integration**
```bash
# Generate QR code for WhatsApp
python unipile_client.py generate_qr whatsapp_qr.png
# Open unipile.html in browser for web interface
```

### **LinkedIn Integration**
```bash
# Authenticate with LinkedIn credentials
python linkedin_client.py auth_credentials <EMAIL> your_password
# Open linkedin.html in browser for web interface
```

### **Instagram Integration**
```bash
# Authenticate with Instagram credentials
python instagram_client.py auth_credentials your_username your_password
# Open instagram.html in browser for web interface
```

### **Telegram Integration**
```bash
# Generate QR code for authentication
python telegram_client.py generate_qr telegram_qr.png
# Scan QR code with Telegram mobile app
# Open telegram.html in browser for web interface
```

### **Facebook Integration**
```bash
# Authenticate with Facebook credentials
python facebook_client.py auth_credentials <EMAIL> your_password
# Open facebook.html in browser for web interface
```

## Security Notice ⚠️

**Never expose your API key in client-side code!** The HTML versions are for development/testing only. In production, always use a backend server to handle API calls and keep your API key secure.

## HTML Version (`unipile.html`)

### Features
- Web-based interface
- QR code generation and display
- Input validation and error handling
- Responsive design

### Usage
1. Open `unipile.html` in a web browser
2. Enter your Unipile API key and DSN
3. Click "Generate QR Code"
4. Scan the QR code with WhatsApp

### Security Recommendations
- Use only for development/testing
- Implement a backend API for production use
- Store API keys securely on the server side

## Python Version (`unipile_client.py`)

### Features
- Command-line interface
- Environment variable support for API keys
- QR code generation and saving
- Account status checking
- Message sending capabilities

### Installation
```bash
pip install -r requirements.txt
```

### Quick Setup
Run the setup script to configure environment:
```bash
# PowerShell (Recommended)
powershell -ExecutionPolicy Bypass -File setup_environment.ps1

# Or Batch
setup_environment.bat
```

### Manual Setup
Set your API key as an environment variable:
```bash
# Windows
set UNIPILE_API_KEY=zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE=
set UNIPILE_DSN=https://api1.unipile.com:13115

# Linux/Mac
export UNIPILE_API_KEY=zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE=
export UNIPILE_DSN=https://api1.unipile.com:13115
```

### Usage

#### Generate QR Code
```bash
python unipile_client.py generate_qr [save_path]
```
Example:
```bash
python unipile_client.py generate_qr whatsapp_qr.png
```

#### Check Account Status
```bash
python unipile_client.py check_status [account_id]
```

#### Send Message
```bash
python unipile_client.py send_message <to> <message> [account_id]
```
Example:
```bash
python unipile_client.py send_message "+**********" "Hello from Unipile!"
```

### Python API Usage
```python
from unipile_client import UnipileWhatsApp

# Initialize client
client = UnipileWhatsApp(api_key="your_api_key", dsn="your_dsn")

# Generate QR code
qr_string = client.generate_qr_code("qr_code.png")
print(f"QR Code: {qr_string}")

# Check status
status = client.check_account_status()
print(f"Status: {status}")

# Send message
result = client.send_message("+**********", "Hello!")
print(f"Message sent: {result}")
```

## Current Configuration

- **API Key**: `zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE=`
- **DSN**: `https://api1.unipile.com:13115`
- **Provider**: `WHATSAPP`

## Environment Variables

- `UNIPILE_API_KEY` - Your Unipile API key (required)
- `UNIPILE_DSN` - Your Unipile DSN endpoint (optional, defaults to https://api1.unipile.com:13115)

## Error Handling

Both implementations include comprehensive error handling:
- Network errors
- API errors
- Invalid responses
- Missing dependencies

## Dependencies

### Python
- `requests` - HTTP client library
- `qrcode[pil]` - QR code generation (optional, for saving QR images)

### HTML
- No external dependencies (uses browser's fetch API)
- QR code images generated via qrserver.com API

## Troubleshooting

### Common Issues

1. **API Key Error**: Make sure your API key is correct and has the necessary permissions
2. **Network Error**: Check your internet connection and DSN endpoint
3. **QR Code Not Displaying**: Verify the API response contains a valid QR code string
4. **Python Import Error**: Install dependencies with `pip install -r requirements.txt`

### Getting Help

- Check the Unipile API documentation
- Verify your API key and DSN are correct
- Ensure your account has WhatsApp integration enabled

## License

This code is provided as-is for educational and development purposes.
