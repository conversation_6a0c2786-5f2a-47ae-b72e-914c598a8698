#!/usr/bin/env python3
"""
Debug script to test LinkedIn integration with Unipile API
"""

import requests
import json

def debug_linkedin_api():
    """Debug the LinkedIn API integration"""
    
    api_key = "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE="
    dsn = "https://api1.unipile.com:13115"
    
    print("🔍 Debugging Unipile LinkedIn API Integration")
    print("=" * 60)
    
    # First, check existing accounts
    print("\n1. Checking existing accounts...")
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'X-API-KEY': api_key,
            'accept': 'application/json'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        print(f"GET /accounts - Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("Existing accounts:")
            print(json.dumps(data, indent=2))
            
            if isinstance(data, dict) and 'items' in data:
                accounts = data['items']
                linkedin_accounts = [acc for acc in accounts if acc.get('type') == 'LINKEDIN']
                print(f"\nFound {len(linkedin_accounts)} existing LinkedIn accounts")
                for i, account in enumerate(linkedin_accounts):
                    print(f"LinkedIn Account {i+1}: ID={account.get('id')}, Name={account.get('name')}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error checking accounts: {e}")
    
    # Now try creating a new LinkedIn account (this will fail without credentials)
    print("\n2. Testing LinkedIn account creation (without credentials)...")
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'Content-Type': 'application/json',
            'X-API-KEY': api_key
        }
        payload = {"provider": "LINKEDIN"}

        response = requests.post(url, headers=headers, json=payload, timeout=30)
        print(f"POST /accounts - Status: {response.status_code}")

        if response.status_code in [200, 201]:
            data = response.json()
            print("Create LinkedIn account response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Expected error (LinkedIn requires credentials): {response.status_code}")
            # Don't print the full error as it's very long

    except Exception as e:
        print(f"Error creating LinkedIn account: {e}")

    print("\n3. LinkedIn Authentication Requirements:")
    print("LinkedIn requires one of these authentication methods:")
    print("1. Basic Authentication:")
    print("   - username: LinkedIn email or phone")
    print("   - password: LinkedIn password")
    print("2. Cookie Authentication:")
    print("   - access_token: LinkedIn li_at cookie value")
    print("\nExample usage:")
    print("python linkedin_client.py auth_credentials <EMAIL> your_password")
    print("python linkedin_client.py auth_token your_li_at_cookie_value")

if __name__ == "__main__":
    debug_linkedin_api()
