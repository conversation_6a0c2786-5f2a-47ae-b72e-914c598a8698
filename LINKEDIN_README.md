# 🔗 LinkedIn Integration with Unipile

This directory contains LinkedIn integration tools using the Unipile API for posting content, managing connections, and accessing LinkedIn data.

## ✅ **Status**
- **API Connection**: ✅ Verified and Working
- **Authentication Methods**: ✅ Both credential and token auth supported
- **LinkedIn Integration**: ✅ Ready to use (requires LinkedIn credentials)

## 📁 **Files**

- `linkedin.html` - Web-based interface for LinkedIn integration
- `linkedin_client.py` - Python script for LinkedIn operations
- `debug_linkedin.py` - Debug script for testing LinkedIn API
- `LINKEDIN_README.md` - This documentation

## 🔐 **Authentication Methods**

LinkedIn requires authentication credentials (unlike WhatsApp which uses QR codes). You have two options:

### **Method 1: Username & Password (Recommended)**
```python
python linkedin_client.py auth_credentials <EMAIL> your_password
```

### **Method 2: Access Token (Advanced)**
```python
python linkedin_client.py auth_token your_li_at_cookie_value
```

To get your `li_at` cookie:
1. Log into LinkedIn.com in your browser
2. Open Developer Tools (F12)
3. Go to Application/Storage → Cookies → linkedin.com
4. Find the `li_at` cookie and copy its value

## 🚀 **Quick Start**

### **HTML Interface (Easiest)**
1. Open `linkedin.html` in your browser
2. Enter your API credentials (pre-filled)
3. Choose authentication method:
   - Enter LinkedIn username/email and password, OR
   - Enter your LinkedIn access token
4. Click "Connect with Credentials" or "Connect with Token"
5. Use the interface to post content, check status, etc.

### **Python Script**
```bash
# 1. Authenticate with LinkedIn
python linkedin_client.py auth_credentials <EMAIL> your_password

# 2. Check account status
python linkedin_client.py check_status

# 3. Post content to LinkedIn
python linkedin_client.py post_content "Hello LinkedIn! This is posted via Unipile API."

# 4. Get profile information
python linkedin_client.py profile
```

## 📋 **Available Operations**

### **1. Authentication**
- **Credentials**: Username/email + password
- **Token**: Access token (li_at cookie)

### **2. Account Management**
- Check account status and connection
- Get profile information
- Monitor account health

### **3. Content Operations**
- Post text content to LinkedIn
- Share updates on your profile
- Manage LinkedIn posts

### **4. Profile Access**
- Retrieve profile information
- Access connection data
- Get account details

## 🔧 **Configuration**

### **Environment Variables**
```bash
# Windows
set UNIPILE_API_KEY=zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE=
set UNIPILE_DSN=https://api1.unipile.com:13115

# Linux/Mac
export UNIPILE_API_KEY=zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE=
export UNIPILE_DSN=https://api1.unipile.com:13115
```

### **Configuration File**
The system also reads from `config.json`:
```json
{
  "api_key": "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE=",
  "dsn": "https://api1.unipile.com:13115"
}
```

## 🛡️ **Security Best Practices**

### **For Development/Testing**
- ✅ Use the HTML interface for quick testing
- ✅ Store credentials in environment variables
- ✅ Never commit credentials to version control

### **For Production**
- ✅ Use backend server to handle API calls
- ✅ Store API keys securely (environment variables, key vaults)
- ✅ Implement proper authentication flows
- ✅ Use HTTPS for all communications

## 🔍 **Debugging**

### **Test API Connection**
```bash
python debug_linkedin.py
```

### **Common Issues**

1. **Authentication Failed**
   - Verify LinkedIn credentials are correct
   - Check if account has 2FA enabled
   - Ensure access token is valid and not expired

2. **API Key Error**
   - Verify API key is correct
   - Check environment variables are set
   - Ensure API key has LinkedIn permissions

3. **Network Errors**
   - Check internet connection
   - Verify DSN endpoint is accessible
   - Check firewall settings

## 📊 **API Response Examples**

### **Successful Authentication**
```json
{
  "object": "Account",
  "account_id": "linkedin_account_123",
  "type": "LINKEDIN",
  "name": "<EMAIL>",
  "created_at": "2025-06-05T00:00:00.000Z",
  "sources": [
    {
      "id": "linkedin_account_123_MESSAGING",
      "status": "OK"
    }
  ]
}
```

### **Post Content Response**
```json
{
  "object": "Message",
  "id": "post_123",
  "text": "Hello LinkedIn!",
  "created_at": "2025-06-05T00:00:00.000Z",
  "status": "sent"
}
```

## 🔗 **Integration Examples**

### **Python Integration**
```python
from linkedin_client import UnipileLinkedIn

# Initialize client
client = UnipileLinkedIn()

# Authenticate
client.authenticate_with_credentials("<EMAIL>", "password")

# Post content
result = client.post_content("Hello from Python!")
print(f"Posted: {result}")

# Get profile
profile = client.get_profile_info()
print(f"Profile: {profile}")
```

### **JavaScript Integration**
```javascript
// Use the HTML interface or implement similar API calls
const response = await fetch(`${dsn}/api/v1/accounts`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-KEY': apiKey
  },
  body: JSON.stringify({
    provider: 'LINKEDIN',
    username: '<EMAIL>',
    password: 'password'
  })
});
```

## 📞 **Support**

- Check the debug output for detailed error information
- Verify all credentials and API keys
- Ensure LinkedIn account is accessible
- Review Unipile API documentation for latest updates

## 🎯 **Next Steps**

1. **Test Authentication**: Use the HTML interface to verify your LinkedIn credentials work
2. **Post Test Content**: Try posting a simple message to verify the integration
3. **Implement in Your App**: Use the Python client or HTML interface as a starting point
4. **Monitor Usage**: Check account status regularly to ensure connection remains active

Your LinkedIn integration is ready to use! 🚀
