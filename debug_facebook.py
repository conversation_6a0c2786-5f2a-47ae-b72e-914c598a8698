#!/usr/bin/env python3
"""
Debug script to test Facebook integration with Unipile API
"""

import requests
import json

def debug_facebook_api():
    """Debug the Facebook API integration"""
    
    api_key = "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE="
    dsn = "https://api1.unipile.com:13115"
    
    print("🔍 Debugging Unipile Facebook API Integration")
    print("=" * 60)
    
    # First, check existing accounts
    print("\n1. Checking existing accounts...")
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'X-API-KEY': api_key,
            'accept': 'application/json'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        print(f"GET /accounts - Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("Existing accounts:")
            print(json.dumps(data, indent=2))
            
            if isinstance(data, dict) and 'items' in data:
                accounts = data['items']
                facebook_accounts = [acc for acc in accounts if acc.get('type') == 'FACEBOOK']
                print(f"\nFound {len(facebook_accounts)} existing Facebook accounts")
                for i, account in enumerate(facebook_accounts):
                    print(f"Facebook Account {i+1}: ID={account.get('id')}, Name={account.get('name')}")
                    
                # Show all platform summary
                platform_summary = {}
                for account in accounts:
                    platform = account.get('type', 'Unknown')
                    platform_summary[platform] = platform_summary.get(platform, 0) + 1
                
                print(f"\n📊 Platform Summary:")
                for platform, count in platform_summary.items():
                    status_icon = "✅" if count > 0 else "❌"
                    print(f"  {status_icon} {platform}: {count} account(s)")
                    
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error checking accounts: {e}")
    
    # Now try creating a new Facebook account (this will fail without credentials)
    print("\n2. Testing Facebook account creation (without credentials)...")
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'Content-Type': 'application/json',
            'X-API-KEY': api_key
        }
        payload = {"provider": "FACEBOOK"}
        
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        print(f"POST /accounts - Status: {response.status_code}")
        
        if response.status_code in [200, 201]:
            data = response.json()
            print("Create Facebook account response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Expected error (Facebook requires credentials): {response.status_code}")
            # Don't print the full error as it's very long
            
    except Exception as e:
        print(f"Error creating Facebook account: {e}")

    print("\n3. Facebook Authentication Requirements:")
    print("Facebook requires username/email and password authentication:")
    print("   - username: Facebook email or username")
    print("   - password: Facebook password")
    print("\nExample usage:")
    print("python facebook_client.py auth_credentials <EMAIL> your_password")
    
    print("\n4. Facebook Features Available:")
    print("✅ Username/email and password authentication")
    print("✅ Timeline posting (text and images)")
    print("✅ Facebook Messenger integration")
    print("✅ Profile information access")
    print("✅ Friends list management")
    print("✅ Facebook Pages management")
    print("✅ Account status monitoring")
    
    print("\n5. Facebook vs Other Platforms:")
    print("📘 Facebook: Username/Password → Posts, Messenger, Pages")
    print("📱 Telegram: QR Code → Channels, Bots, Groups")
    print("📱 Instagram: Username/Password → Visual content, DMs")
    print("💼 LinkedIn: Username/Password → Professional posts")
    print("📞 WhatsApp: QR Code → Personal/Business messaging")
    
    print("\n6. Common Facebook Use Cases:")
    print("🎯 Automated timeline posting")
    print("🎯 Facebook Page management")
    print("🎯 Messenger customer support")
    print("🎯 Social media marketing campaigns")
    print("🎯 Friend and follower engagement")
    print("🎯 Event promotion and management")
    print("🎯 Business page automation")
    
    print("\n7. Facebook Authentication Flow:")
    print("Step 1: 📧 Enter email/username and password")
    print("Step 2: ✅ Account authenticated (may require 2FA)")
    print("Step 3: 🚀 Access posts, messages, pages")
    
    print("\n8. Platform Comparison Summary:")
    platforms = [
        {"name": "Facebook", "auth": "Email/Password", "strength": "Posts & Pages"},
        {"name": "Telegram", "auth": "QR Code", "strength": "Channels & Bots"},
        {"name": "Instagram", "auth": "Username/Password", "strength": "Visual Content"},
        {"name": "LinkedIn", "auth": "Email/Password", "strength": "Professional"},
        {"name": "WhatsApp", "auth": "QR Code", "strength": "Personal Messaging"}
    ]
    
    print("┌─────────────┬─────────────────┬─────────────────────┐")
    print("│ Platform    │ Authentication  │ Primary Strength    │")
    print("├─────────────┼─────────────────┼─────────────────────┤")
    for platform in platforms:
        print(f"│ {platform['name']:<11} │ {platform['auth']:<15} │ {platform['strength']:<19} │")
    print("└─────────────┴─────────────────┴─────────────────────┘")
    
    print("\n9. Facebook Unique Features:")
    print("🎯 Facebook Pages: Manage business pages and fan pages")
    print("🎯 Facebook Groups: Community management and engagement")
    print("🎯 Facebook Events: Event creation and promotion")
    print("🎯 Facebook Marketplace: Product listings and sales")
    print("🎯 Facebook Ads: Advertising campaign management")
    print("🎯 Facebook Live: Live streaming capabilities")
    
    print("\n10. Integration Benefits:")
    print("✅ Largest social network reach (3+ billion users)")
    print("✅ Comprehensive business tools and pages")
    print("✅ Advanced targeting and advertising options")
    print("✅ Multi-format content support (text, images, videos)")
    print("✅ Messenger integration for customer support")
    print("✅ Event and community management features")

if __name__ == "__main__":
    debug_facebook_api()
