#!/usr/bin/env python3
"""
Unipile LinkedIn Integration Script

This script provides a Python interface for connecting LinkedIn accounts
using the Unipile API. It includes functions for OAuth authentication,
posting content, and managing LinkedIn connections.

Security Note: Store your API key in environment variables or a secure config file.
Never hardcode API keys in your source code.
"""

import os
import sys
import json
import requests
from typing import Optional, Dict, Any, List
import logging


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class UnipileLinkedIn:
    """
    A Python client for Unipile LinkedIn API integration.
    """
    
    def __init__(self, api_key: Optional[str] = None, dsn: Optional[str] = None):
        """
        Initialize the Unipile LinkedIn client.
        
        Args:
            api_key: Unipile API key (if not provided, will look for UNIPILE_API_KEY env var or config.json)
            dsn: Unipile DSN endpoint (if not provided, will look for UNIPILE_DSN env var or config.json)
        """
        # Try to load from config file if no parameters provided
        config = self._load_config()
        
        self.api_key = api_key or os.getenv('UNIPILE_API_KEY') or config.get('api_key')
        self.dsn = dsn or os.getenv('UNIPILE_DSN') or config.get('dsn', 'https://api1.unipile.com:13115')
        
        if not self.api_key:
            raise ValueError("API key is required. Set UNIPILE_API_KEY environment variable or pass api_key parameter.")
        
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'X-API-KEY': self.api_key
        })
        
        self.account_id = None
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from config.json if it exists"""
        try:
            if os.path.exists('config.json'):
                with open('config.json', 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.debug(f"Could not load config.json: {e}")
        return {}
        
    def create_linkedin_account(self, username: str, password: str) -> Dict[str, Any]:
        """
        Create a new LinkedIn account session using basic authentication.

        Args:
            username: LinkedIn username (email or phone)
            password: LinkedIn password

        Returns:
            Dict containing account information
        """
        try:
            url = f"{self.dsn}/api/v1/accounts"
            payload = {
                "provider": "LINKEDIN",
                "username": username,
                "password": password
            }

            logger.info("Creating LinkedIn account session...")
            response = self.session.post(url, json=payload)
            response.raise_for_status()

            data = response.json()
            self.account_id = data.get('account_id')

            logger.info(f"LinkedIn account created successfully. Account ID: {self.account_id}")
            logger.debug(f"Full API Response: {json.dumps(data, indent=2)}")
            return data

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to create LinkedIn account: {e}")
            raise

    def create_linkedin_account_with_token(self, access_token: str) -> Dict[str, Any]:
        """
        Create a new LinkedIn account session using cookie authentication.

        Args:
            access_token: LinkedIn access token (li_at cookie value)

        Returns:
            Dict containing account information
        """
        try:
            url = f"{self.dsn}/api/v1/accounts"
            payload = {
                "provider": "LINKEDIN",
                "access_token": access_token
            }

            logger.info("Creating LinkedIn account session with token...")
            response = self.session.post(url, json=payload)
            response.raise_for_status()

            data = response.json()
            self.account_id = data.get('account_id')

            logger.info(f"LinkedIn account created successfully. Account ID: {self.account_id}")
            logger.debug(f"Full API Response: {json.dumps(data, indent=2)}")
            return data

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to create LinkedIn account with token: {e}")
            raise
    
    def authenticate_with_credentials(self, username: str, password: str) -> Dict[str, Any]:
        """
        Authenticate LinkedIn account with username and password.

        Args:
            username: LinkedIn username (email or phone)
            password: LinkedIn password

        Returns:
            Account data
        """
        return self.create_linkedin_account(username, password)

    def authenticate_with_token(self, access_token: str) -> Dict[str, Any]:
        """
        Authenticate LinkedIn account with access token.

        Args:
            access_token: LinkedIn access token (li_at cookie value)

        Returns:
            Account data
        """
        return self.create_linkedin_account_with_token(access_token)
    
    def check_account_status(self, account_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Check the status of a LinkedIn account.
        
        Args:
            account_id: Account ID to check (uses self.account_id if not provided)
            
        Returns:
            Dict containing account status information
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError("No account ID available. Create an account first.")
        
        try:
            url = f"{self.dsn}/api/v1/accounts/{account_id}"
            response = self.session.get(url)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to check account status: {e}")
            raise
    
    def post_content(self, text: str, account_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Post content to LinkedIn.
        
        Args:
            text: Content text to post
            account_id: Account ID to use (uses self.account_id if not provided)
            
        Returns:
            Dict containing post response
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError("No account ID available. Create an account first.")
        
        try:
            url = f"{self.dsn}/api/v1/accounts/{account_id}/messages"
            payload = {
                "text": text,
                "type": "POST"
            }
            
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            logger.info(f"Content posted successfully to LinkedIn")
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to post content: {e}")
            raise
    
    def get_profile_info(self, account_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get LinkedIn profile information.
        
        Args:
            account_id: Account ID to use (uses self.account_id if not provided)
            
        Returns:
            Dict containing profile information
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError("No account ID available. Create an account first.")
        
        try:
            url = f"{self.dsn}/api/v1/accounts/{account_id}/profile"
            response = self.session.get(url)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get profile info: {e}")
            raise


def main():
    """
    Main function for command-line usage.
    """
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python linkedin_client.py auth_credentials <username> <password>")
        print("  python linkedin_client.py auth_token <access_token>")
        print("  python linkedin_client.py check_status [account_id]")
        print("  python linkedin_client.py post_content <text> [account_id]")
        print("  python linkedin_client.py profile [account_id]")
        print("\nAuthentication methods:")
        print("  auth_credentials - Use LinkedIn username/email and password")
        print("  auth_token - Use LinkedIn access token (li_at cookie)")
        print("\nEnvironment variables:")
        print("  UNIPILE_API_KEY - Your Unipile API key")
        print("  UNIPILE_DSN - Your Unipile DSN (optional)")
        sys.exit(1)

    command = sys.argv[1]

    try:
        client = UnipileLinkedIn()

        if command == "auth_credentials":
            if len(sys.argv) < 4:
                print("Error: auth_credentials requires <username> and <password> arguments")
                sys.exit(1)

            username = sys.argv[2]
            password = sys.argv[3]

            result = client.authenticate_with_credentials(username, password)
            print(f"✅ LinkedIn authentication successful!")
            print(f"Account ID: {client.account_id}")
            print(f"Response: {json.dumps(result, indent=2)}")

        elif command == "auth_token":
            if len(sys.argv) < 3:
                print("Error: auth_token requires <access_token> argument")
                sys.exit(1)

            access_token = sys.argv[2]

            result = client.authenticate_with_token(access_token)
            print(f"✅ LinkedIn token authentication successful!")
            print(f"Account ID: {client.account_id}")
            print(f"Response: {json.dumps(result, indent=2)}")

        elif command == "check_status":
            account_id = sys.argv[2] if len(sys.argv) > 2 else None
            status = client.check_account_status(account_id)
            print(f"Account Status: {json.dumps(status, indent=2)}")
            
        elif command == "post_content":
            if len(sys.argv) < 3:
                print("Error: post_content requires <text> argument")
                sys.exit(1)
            
            text = sys.argv[2]
            account_id = sys.argv[3] if len(sys.argv) > 3 else None
            
            result = client.post_content(text, account_id)
            print(f"Content posted: {json.dumps(result, indent=2)}")
            
        elif command == "profile":
            account_id = sys.argv[2] if len(sys.argv) > 2 else None
            profile = client.get_profile_info(account_id)
            print(f"Profile Info: {json.dumps(profile, indent=2)}")
            
        else:
            print(f"Unknown command: {command}")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
