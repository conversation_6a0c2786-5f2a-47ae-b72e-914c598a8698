#!/usr/bin/env python3
"""
Test LinkedIn authentication with detailed debugging
"""

import requests
import json
import sys

def test_linkedin_auth_with_debug(username, password):
    """Test LinkedIn authentication with detailed output"""
    
    api_key = "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE="
    dsn = "https://api1.unipile.com:13115"
    
    print("🔍 Testing LinkedIn Authentication with Debug")
    print("=" * 60)
    print(f"Username: {username}")
    print(f"Password: {'*' * len(password)}")
    print(f"API Key: {api_key[:20]}...")
    print(f"DSN: {dsn}")
    print()
    
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'Content-Type': 'application/json',
            'X-API-KEY': api_key
        }
        
        payload = {
            "provider": "LINKEDIN",
            "username": username,
            "password": password
        }
        
        print("📤 Sending authentication request...")
        print(f"URL: {url}")
        print(f"Headers: {headers}")
        print(f"Payload: {json.dumps({**payload, 'password': '***'}, indent=2)}")
        print()
        
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        print("📥 Response received:")
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print()
        
        if response.status_code in [200, 201]:
            print("✅ SUCCESS! LinkedIn authentication worked!")
            data = response.json()
            print("Response Data:")
            print(json.dumps(data, indent=2))
            
            account_id = data.get('account_id')
            if account_id:
                print(f"\n🎯 Account ID: {account_id}")
                print("This account should now appear in your dashboard!")
            
            return True, data
            
        elif response.status_code == 400:
            print("❌ BAD REQUEST - Authentication failed")
            try:
                error_data = response.json()
                print("Error Details:")
                print(json.dumps(error_data, indent=2))
                
                # Check for specific error messages
                error_detail = error_data.get('detail', '')
                if 'invalid' in error_detail.lower():
                    print("\n💡 Likely cause: Invalid LinkedIn credentials")
                elif 'parameters' in error_detail.lower():
                    print("\n💡 Likely cause: Missing or incorrect parameters")
                else:
                    print("\n💡 Check the error details above")
                    
            except:
                print("Raw error response:")
                print(response.text)
            
            return False, None
            
        elif response.status_code == 401:
            print("❌ UNAUTHORIZED - API key issue")
            print("Check if your API key is correct and has LinkedIn permissions")
            return False, None
            
        else:
            print(f"❌ UNEXPECTED ERROR: {response.status_code}")
            print("Response:")
            print(response.text)
            return False, None
            
    except requests.exceptions.Timeout:
        print("❌ TIMEOUT - Request took too long")
        return False, None
    except requests.exceptions.ConnectionError:
        print("❌ CONNECTION ERROR - Cannot reach API")
        return False, None
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        return False, None

def main():
    if len(sys.argv) != 3:
        print("Usage: python test_linkedin_auth_debug.py <username> <password>")
        print("Example: python test_linkedin_auth_debug.py <EMAIL> mypassword")
        sys.exit(1)
    
    username = sys.argv[1]
    password = sys.argv[2]
    
    success, data = test_linkedin_auth_with_debug(username, password)
    
    if success:
        print("\n🎉 Authentication successful!")
        print("Check your Unipile dashboard to see the new LinkedIn account.")
        print("Dashboard: https://dashboard.unipile.com")
    else:
        print("\n💔 Authentication failed!")
        print("Please check the error details above and try again.")

if __name__ == "__main__":
    main()
