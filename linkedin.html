<!DOCTYPE html>
<html>
<head>
  <title>Connect LinkedIn with Unipile</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 50px auto;
      padding: 20px;
      background-color: #f8f9fa;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .linkedin-logo {
      color: #0077B5;
      font-size: 24px;
      font-weight: bold;
    }
    button {
      background-color: #0077B5;
      color: white;
      border: none;
      padding: 12px 24px;
      font-size: 16px;
      border-radius: 5px;
      cursor: pointer;
      margin: 10px 5px;
      min-width: 150px;
    }
    button:hover {
      background-color: #005885;
    }
    button:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
    .error {
      color: red;
      margin-top: 10px;
      padding: 10px;
      background-color: #ffe6e6;
      border-radius: 5px;
    }
    .success {
      color: green;
      margin-top: 10px;
      padding: 10px;
      background-color: #e6ffe6;
      border-radius: 5px;
    }
    .warning {
      background-color: #fff3cd;
      border: 1px solid #ffeaa7;
      color: #856404;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .config-section {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    input[type="text"], textarea {
      width: 100%;
      padding: 8px;
      margin: 5px 0;
      border: 1px solid #ddd;
      border-radius: 3px;
      box-sizing: border-box;
    }
    textarea {
      height: 100px;
      resize: vertical;
    }
    .section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #e0e0e0;
      border-radius: 5px;
    }
    .section h3 {
      margin-top: 0;
      color: #0077B5;
    }
    .oauth-container {
      text-align: center;
      padding: 20px;
      background-color: #f0f8ff;
      border-radius: 5px;
      margin: 20px 0;
    }
    .account-info {
      background-color: #e8f5e8;
      padding: 15px;
      border-radius: 5px;
      margin: 10px 0;
    }
    .button-group {
      text-align: center;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1><span class="linkedin-logo">LinkedIn</span> Integration with Unipile</h1>
    </div>
    
    <div class="warning">
      <strong>⚠️ Security Notice:</strong> Never expose your API key in client-side code. 
      This is for development/testing only. In production, use a backend server to handle API calls.
    </div>

    <div class="config-section">
      <h3>Configuration</h3>
      <label for="apiKey">API Key:</label>
      <input type="text" id="apiKey" value="zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE=" placeholder="Enter your Unipile API key" />
      
      <label for="dsn">DSN:</label>
      <input type="text" id="dsn" value="https://api1.unipile.com:13115" placeholder="Enter your DSN" />
    </div>

    <!-- Authentication Section -->
    <div class="section">
      <h3>1. Connect LinkedIn Account</h3>
      <p>Enter your LinkedIn credentials to connect your account:</p>

      <div style="margin-bottom: 20px;">
        <label for="username">LinkedIn Username/Email:</label>
        <input type="text" id="username" placeholder="Enter your LinkedIn username or email" />

        <label for="password">LinkedIn Password:</label>
        <input type="password" id="password" placeholder="Enter your LinkedIn password" />

        <div class="button-group">
          <button onclick="authenticateWithCredentials()">Connect LinkedIn Account</button>
        </div>
      </div>

      <div id="authContainer"></div>
    </div>

    <!-- Account Status Section -->
    <div class="section">
      <h3>2. Check Account Status</h3>
      <p>Check if your LinkedIn account is connected and active.</p>
      <div class="button-group">
        <button onclick="checkAccountStatus()">Check Status</button>
      </div>
      <div id="statusContainer"></div>
    </div>

    <!-- Post Content Section -->
    <div class="section">
      <h3>3. Post Content to LinkedIn</h3>
      <p>Share content on your LinkedIn profile.</p>
      <label for="postContent">Content to post:</label>
      <textarea id="postContent" placeholder="Enter your LinkedIn post content here..."></textarea>
      <div class="button-group">
        <button onclick="postContent()">Post to LinkedIn</button>
      </div>
      <div id="postContainer"></div>
    </div>

    <!-- Profile Info Section -->
    <div class="section">
      <h3>4. Get Profile Information</h3>
      <p>Retrieve your LinkedIn profile information.</p>
      <div class="button-group">
        <button onclick="getProfile()">Get Profile Info</button>
      </div>
      <div id="profileContainer"></div>
    </div>

    <div id="message"></div>
  </div>

  <script>
    let currentAccountId = null;

    async function makeAPICall(endpoint, method = 'GET', body = null) {
      const apiKey = document.getElementById('apiKey').value.trim();
      const dsn = document.getElementById('dsn').value.trim();
      
      if (!apiKey || !dsn) {
        throw new Error('Please enter both API key and DSN');
      }

      const headers = {
        'X-API-KEY': apiKey,
        'accept': 'application/json'
      };

      if (method !== 'GET') {
        headers['Content-Type'] = 'application/json';
      }

      const config = {
        method: method,
        headers: headers
      };

      if (body) {
        config.body = JSON.stringify(body);
      }

      const response = await fetch(`${dsn}${endpoint}`, config);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return await response.json();
    }

    async function authenticateWithCredentials() {
      const container = document.getElementById('authContainer');
      const username = document.getElementById('username').value.trim();
      const password = document.getElementById('password').value.trim();

      try {
        if (!username || !password) {
          throw new Error('Please enter both username and password');
        }

        container.innerHTML = '<p>Authenticating with LinkedIn...</p>';

        const data = await makeAPICall('/api/v1/accounts', 'POST', {
          provider: 'LINKEDIN',
          username: username,
          password: password
        });

        console.log('Auth Response:', data);
        currentAccountId = data.account_id;

        container.innerHTML = `
          <div class="account-info">
            <h4>✅ LinkedIn Authentication Successful!</h4>
            <p><strong>Account ID:</strong> ${currentAccountId}</p>
            <pre>${JSON.stringify(data, null, 2)}</pre>
          </div>
        `;

        showMessage('LinkedIn account connected successfully!', 'success');

        // Clear password field for security
        document.getElementById('password').value = '';

      } catch (error) {
        console.error('Auth Error:', error);
        showMessage(`Failed to authenticate: ${error.message}`, 'error');
        container.innerHTML = '';
      }
    }



    async function checkAccountStatus() {
      const container = document.getElementById('statusContainer');
      
      try {
        if (!currentAccountId) {
          throw new Error('No account ID available. Please start OAuth first.');
        }

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}`);
        console.log('Status Response:', data);

        container.innerHTML = `
          <div class="account-info">
            <h4>Account Status</h4>
            <pre>${JSON.stringify(data, null, 2)}</pre>
          </div>
        `;
        
        showMessage('Account status retrieved successfully!', 'success');

      } catch (error) {
        console.error('Status Error:', error);
        showMessage(`Failed to check status: ${error.message}`, 'error');
        container.innerHTML = '';
      }
    }

    async function postContent() {
      const container = document.getElementById('postContainer');
      const content = document.getElementById('postContent').value.trim();
      
      try {
        if (!currentAccountId) {
          throw new Error('No account ID available. Please start OAuth first.');
        }

        if (!content) {
          throw new Error('Please enter content to post.');
        }

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/messages`, 'POST', {
          text: content,
          type: 'POST'
        });
        
        console.log('Post Response:', data);

        container.innerHTML = `
          <div class="account-info">
            <h4>✅ Content Posted Successfully!</h4>
            <p><strong>Posted:</strong> "${content}"</p>
            <pre>${JSON.stringify(data, null, 2)}</pre>
          </div>
        `;
        
        showMessage('Content posted to LinkedIn successfully!', 'success');
        document.getElementById('postContent').value = '';

      } catch (error) {
        console.error('Post Error:', error);
        showMessage(`Failed to post content: ${error.message}`, 'error');
        container.innerHTML = '';
      }
    }

    async function getProfile() {
      const container = document.getElementById('profileContainer');
      
      try {
        if (!currentAccountId) {
          throw new Error('No account ID available. Please start OAuth first.');
        }

        const data = await makeAPICall(`/api/v1/accounts/${currentAccountId}/profile`);
        console.log('Profile Response:', data);

        container.innerHTML = `
          <div class="account-info">
            <h4>LinkedIn Profile Information</h4>
            <pre>${JSON.stringify(data, null, 2)}</pre>
          </div>
        `;
        
        showMessage('Profile information retrieved successfully!', 'success');

      } catch (error) {
        console.error('Profile Error:', error);
        showMessage(`Failed to get profile: ${error.message}`, 'error');
        container.innerHTML = '';
      }
    }

    function showMessage(text, type) {
      const messageDiv = document.getElementById('message');
      messageDiv.textContent = text;
      messageDiv.className = type;
      
      // Auto-clear message after 5 seconds
      setTimeout(() => {
        messageDiv.textContent = '';
        messageDiv.className = '';
      }, 5000);
    }

    // Auto-focus on API key input
    document.addEventListener('DOMContentLoaded', function() {
      // API key is pre-filled, so focus on the OAuth button instead
      document.getElementById('oauthBtn').focus();
    });
  </script>
</body>
</html>
