# PowerShell script to set up Unipile environment and test API connection

Write-Host "Setting up Unipile environment variables..." -ForegroundColor Green
Write-Host ""

# Set environment variables
$env:UNIPILE_API_KEY = "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE="
$env:UNIPILE_DSN = "https://api1.unipile.com:13115"

Write-Host "Environment variables set:" -ForegroundColor Yellow
Write-Host "UNIPILE_API_KEY: $($env:UNIPILE_API_KEY.Substring(0,20))..."
Write-Host "UNIPILE_DSN: $env:UNIPILE_DSN"
Write-Host ""

Write-Host "Testing API connection..." -ForegroundColor Green
Write-Host ""

try {
    # Test API connection
    $headers = @{
        "X-API-KEY" = $env:UNIPILE_API_KEY
        "accept" = "application/json"
    }
    
    $response = Invoke-WebRequest -Uri "$env:UNIPILE_DSN/api/v1/accounts" -Headers $headers -Method GET -TimeoutSec 30
    
    Write-Host "✅ API Connection Successful!" -ForegroundColor Green
    Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Cyan
    Write-Host "Response:" -ForegroundColor Cyan
    Write-Host $response.Content -ForegroundColor White
    
} catch {
    Write-Host "❌ API Connection Failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Testing WhatsApp account creation..." -ForegroundColor Green
Write-Host ""

try {
    # Test WhatsApp account creation
    $headers = @{
        "X-API-KEY" = $env:UNIPILE_API_KEY
        "Content-Type" = "application/json"
    }
    
    $body = @{
        provider = "WHATSAPP"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "$env:UNIPILE_DSN/api/v1/accounts" -Headers $headers -Method POST -Body $body -TimeoutSec 30
    
    Write-Host "✅ WhatsApp Account Creation Successful!" -ForegroundColor Green
    Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Cyan
    Write-Host "Response:" -ForegroundColor Cyan
    
    $responseData = $response.Content | ConvertFrom-Json
    Write-Host ($responseData | ConvertTo-Json -Depth 3) -ForegroundColor White
    
    if ($responseData.qrCodeString) {
        Write-Host ""
        Write-Host "✅ QR Code String received!" -ForegroundColor Green
        Write-Host "QR Code: $($responseData.qrCodeString.Substring(0,50))..." -ForegroundColor Cyan
    }
    
} catch {
    Write-Host "❌ WhatsApp Account Creation Failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        try {
            $errorContent = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorContent)
            $errorText = $reader.ReadToEnd()
            Write-Host "Error Response: $errorText" -ForegroundColor Red
        } catch {
            Write-Host "Could not read error response" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "Environment setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "You can now use:" -ForegroundColor Yellow
Write-Host "1. Python script: python unipile_client.py generate_qr" -ForegroundColor Cyan
Write-Host "2. HTML interface: Open unipile.html in browser" -ForegroundColor Cyan
Write-Host ""
Write-Host "API Configuration:" -ForegroundColor Yellow
Write-Host "API Key: $($env:UNIPILE_API_KEY.Substring(0,20))..."
Write-Host "DSN: $env:UNIPILE_DSN"
