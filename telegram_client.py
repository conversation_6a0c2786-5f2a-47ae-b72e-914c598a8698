#!/usr/bin/env python3
"""
Unipile Telegram Integration Script

This script provides a Python interface for connecting Telegram accounts
using the Unipile API. It includes functions for authentication,
sending messages, and managing Telegram interactions.

Security Note: Store your API key in environment variables or a secure config file.
Never hardcode API keys in your source code.
"""

import os
import sys
import json
import requests
from typing import Optional, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class UnipileTelegram:
    """
    A Python client for Unipile Telegram API integration.
    """
    
    def __init__(self, api_key: Optional[str] = None, dsn: Optional[str] = None):
        """
        Initialize the Unipile Telegram client.
        
        Args:
            api_key: Unipile API key (if not provided, will look for UNIPILE_API_KEY env var or config.json)
            dsn: Unipile DSN endpoint (if not provided, will look for UNIPILE_DSN env var or config.json)
        """
        # Try to load from config file if no parameters provided
        config = self._load_config()
        
        self.api_key = api_key or os.getenv('UNIPILE_API_KEY') or config.get('api_key')
        self.dsn = dsn or os.getenv('UNIPILE_DSN') or config.get('dsn', 'https://api1.unipile.com:13115')
        
        if not self.api_key:
            raise ValueError("API key is required. Set UNIPILE_API_KEY environment variable or pass api_key parameter.")
        
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'X-API-KEY': self.api_key
        })
        
        self.account_id = None
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from config.json if it exists"""
        try:
            if os.path.exists('config.json'):
                with open('config.json', 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.debug(f"Could not load config.json: {e}")
        return {}
        
    def create_telegram_account(self) -> Dict[str, Any]:
        """
        Create a new Telegram account session and get QR code.

        Returns:
            Dict containing account information and QR code for authentication
        """
        try:
            url = f"{self.dsn}/api/v1/accounts"
            payload = {"provider": "TELEGRAM"}

            logger.info("Creating Telegram account session...")
            response = self.session.post(url, json=payload)
            response.raise_for_status()

            data = response.json()
            self.account_id = data.get('account_id')

            logger.info(f"Telegram account created successfully. Account ID: {self.account_id}")
            logger.debug(f"Full API Response: {json.dumps(data, indent=2)}")
            return data

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to create Telegram account: {e}")
            raise
    
    def verify_phone_code(self, verification_code: str, account_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Verify the phone number with the code received via SMS.
        
        Args:
            verification_code: Code received via SMS
            account_id: Account ID to verify (uses self.account_id if not provided)
            
        Returns:
            Dict containing verification response
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError("No account ID available. Create an account first.")
        
        try:
            url = f"{self.dsn}/api/v1/accounts/{account_id}/verify"
            payload = {
                "code": verification_code
            }
            
            logger.info("Verifying phone code...")
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            data = response.json()
            logger.info("Phone verification successful!")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to verify phone code: {e}")
            raise
    
    def get_qr_code(self) -> str:
        """
        Get QR code for Telegram authentication.

        Returns:
            QR code string for Telegram login
        """
        account_data = self.create_telegram_account()

        # Handle different response structures for QR code
        qr_string = None

        if 'qrcode' in account_data:
            qr_string = account_data['qrcode']
        elif 'checkpoint' in account_data:
            checkpoint = account_data['checkpoint']
            if isinstance(checkpoint, dict):
                if 'qrcode' in checkpoint:
                    qr_string = checkpoint['qrcode']
                elif 'qr_code' in checkpoint:
                    qr_string = checkpoint['qr_code']
        elif 'qr_code' in account_data:
            qr_string = account_data['qr_code']

        if not qr_string:
            logger.error(f"Could not find QR code in API response.")
            logger.error(f"Full API Response: {json.dumps(account_data, indent=2)}")
            raise ValueError("No QR code string received from API. Check the response structure above.")

        return qr_string

    def generate_qr_code(self, filename: Optional[str] = None) -> str:
        """
        Generate QR code for Telegram authentication and optionally save as image.

        Args:
            filename: Optional filename to save QR code image

        Returns:
            QR code string
        """
        qr_string = self.get_qr_code()

        if filename:
            try:
                import qrcode


                # Generate QR code
                qr = qrcode.QRCode(
                    version=1,
                    error_correction=qrcode.constants.ERROR_CORRECT_L,
                    box_size=10,
                    border=4,
                )
                qr.add_data(qr_string)
                qr.make(fit=True)

                # Create image
                img = qr.make_image(fill_color="black", back_color="white")
                img.save(filename)

                logger.info(f"QR code saved to: {filename}")

            except ImportError:
                logger.warning("qrcode library not installed. Install with: pip install qrcode[pil]")
                logger.info("QR code string returned without image generation")
            except Exception as e:
                logger.error(f"Failed to save QR code image: {e}")

        return qr_string
    
    def check_account_status(self, account_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Check the status of a Telegram account.
        
        Args:
            account_id: Account ID to check (uses self.account_id if not provided)
            
        Returns:
            Dict containing account status information
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError("No account ID available. Create an account first.")
        
        try:
            url = f"{self.dsn}/api/v1/accounts/{account_id}"
            response = self.session.get(url)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to check account status: {e}")
            raise
    
    def send_message(self, to: str, message: str, account_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Send a message on Telegram.
        
        Args:
            to: Recipient username, phone number, or chat ID
            message: Message text to send
            account_id: Account ID to use (uses self.account_id if not provided)
            
        Returns:
            Dict containing message send response
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError("No account ID available. Create an account first.")
        
        try:
            url = f"{self.dsn}/api/v1/accounts/{account_id}/messages"
            payload = {
                "to": to,
                "text": message
            }
            
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            logger.info(f"Message sent successfully to {to}")
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to send message: {e}")
            raise
    
    def send_to_channel(self, channel: str, message: str, account_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Send a message to a Telegram channel.
        
        Args:
            channel: Channel username or ID (e.g., @mychannel)
            message: Message text to send
            account_id: Account ID to use (uses self.account_id if not provided)
            
        Returns:
            Dict containing message send response
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError("No account ID available. Create an account first.")
        
        try:
            url = f"{self.dsn}/api/v1/accounts/{account_id}/channels/{channel}/messages"
            payload = {
                "text": message
            }
            
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            logger.info(f"Message sent successfully to channel {channel}")
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to send message to channel: {e}")
            raise
    
    def get_profile_info(self, account_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get Telegram profile information.
        
        Args:
            account_id: Account ID to use (uses self.account_id if not provided)
            
        Returns:
            Dict containing profile information
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError("No account ID available. Create an account first.")
        
        try:
            url = f"{self.dsn}/api/v1/accounts/{account_id}/profile"
            response = self.session.get(url)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get profile info: {e}")
            raise
    
    def get_chats(self, account_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get list of Telegram chats.
        
        Args:
            account_id: Account ID to use (uses self.account_id if not provided)
            
        Returns:
            Dict containing chats information
        """
        account_id = account_id or self.account_id
        if not account_id:
            raise ValueError("No account ID available. Create an account first.")
        
        try:
            url = f"{self.dsn}/api/v1/accounts/{account_id}/chats"
            response = self.session.get(url)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get chats: {e}")
            raise


def main():
    """
    Main function for command-line usage.
    """
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python telegram_client.py generate_qr [filename]")
        print("  python telegram_client.py check_status [account_id]")
        print("  python telegram_client.py send_message <to> <message> [account_id]")
        print("  python telegram_client.py send_channel <channel> <message> [account_id]")
        print("  python telegram_client.py profile [account_id]")
        print("  python telegram_client.py chats [account_id]")
        print("\nAuthentication:")
        print("  generate_qr - Generate QR code for Telegram authentication")
        print("  Scan the QR code with Telegram mobile app to authenticate")
        print("\nEnvironment variables:")
        print("  UNIPILE_API_KEY - Your Unipile API key")
        print("  UNIPILE_DSN - Your Unipile DSN (optional)")
        sys.exit(1)
    
    command = sys.argv[1]
    
    try:
        client = UnipileTelegram()
        
        if command == "generate_qr":
            filename = sys.argv[2] if len(sys.argv) > 2 else None

            qr_string = client.generate_qr_code(filename)
            print(f"✅ Telegram QR code generated!")
            print(f"Account ID: {client.account_id}")

            if filename:
                print(f"QR code saved to: {filename}")
            else:
                print("QR code string:")
                print(qr_string)

            print("\nTo authenticate:")
            print("1. Open Telegram mobile app")
            print("2. Go to Settings > Devices > Link Desktop Device")
            print("3. Scan the QR code")
            print("4. Check account status with:")
            print(f"   python telegram_client.py check_status {client.account_id}")

        elif command == "check_status":
            account_id = sys.argv[2] if len(sys.argv) > 2 else None
            status = client.check_account_status(account_id)
            print(f"Account Status: {json.dumps(status, indent=2)}")
            
        elif command == "send_message":
            if len(sys.argv) < 4:
                print("Error: send_message requires <to> and <message> arguments")
                sys.exit(1)
            
            to = sys.argv[2]
            message = sys.argv[3]
            account_id = sys.argv[4] if len(sys.argv) > 4 else None
            
            result = client.send_message(to, message, account_id)
            print(f"Message sent: {json.dumps(result, indent=2)}")
            
        elif command == "send_channel":
            if len(sys.argv) < 4:
                print("Error: send_channel requires <channel> and <message> arguments")
                sys.exit(1)
            
            channel = sys.argv[2]
            message = sys.argv[3]
            account_id = sys.argv[4] if len(sys.argv) > 4 else None
            
            result = client.send_to_channel(channel, message, account_id)
            print(f"Channel message sent: {json.dumps(result, indent=2)}")
            
        elif command == "profile":
            account_id = sys.argv[2] if len(sys.argv) > 2 else None
            profile = client.get_profile_info(account_id)
            print(f"Profile Info: {json.dumps(profile, indent=2)}")
            
        elif command == "chats":
            account_id = sys.argv[2] if len(sys.argv) > 2 else None
            chats = client.get_chats(account_id)
            print(f"Chats: {json.dumps(chats, indent=2)}")
            
        else:
            print(f"Unknown command: {command}")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
