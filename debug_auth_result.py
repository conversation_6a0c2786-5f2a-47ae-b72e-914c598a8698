#!/usr/bin/env python3
"""
Debug script to check authentication results and troubleshoot dashboard issues
"""

import requests
import json

def debug_authentication_result():
    """Debug what happened with authentication"""
    
    api_key = "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE="
    dsn = "https://api1.unipile.com:13115"
    
    print("🔍 Debugging Authentication Result")
    print("=" * 60)
    
    # Check all accounts with detailed info
    print("\n1. Checking ALL accounts (detailed)...")
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'X-API-KEY': api_key,
            'accept': 'application/json'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        print(f"GET /accounts - Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("Full API Response:")
            print(json.dumps(data, indent=2))
            
            if isinstance(data, dict) and 'items' in data:
                accounts = data['items']
                print(f"\n📊 Found {len(accounts)} total accounts")
                
                if len(accounts) == 0:
                    print("❌ No accounts found - Authentication may have failed")
                    print("\n🔍 Possible reasons:")
                    print("1. LinkedIn credentials were incorrect")
                    print("2. LinkedIn account has 2FA enabled")
                    print("3. LinkedIn blocked the authentication")
                    print("4. API key doesn't have LinkedIn permissions")
                    print("5. Different API endpoint needed")
                else:
                    for i, account in enumerate(accounts):
                        print(f"\n✅ Account {i+1}:")
                        print(f"   Type: {account.get('type')}")
                        print(f"   Name: {account.get('name')}")
                        print(f"   ID: {account.get('id')}")
                        print(f"   Created: {account.get('created_at')}")
                        print(f"   Status: {account.get('sources', [{}])[0].get('status', 'Unknown')}")
            
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Error Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request Error: {e}")
    
    # Try to test a simple LinkedIn auth to see what happens
    print("\n2. Testing LinkedIn authentication requirements...")
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'Content-Type': 'application/json',
            'X-API-KEY': api_key
        }
        
        # Test with minimal payload to see error
        payload = {"provider": "LINKEDIN"}
        
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        print(f"POST /accounts (test) - Status: {response.status_code}")
        
        if response.status_code == 400:
            print("✅ Expected 400 error - LinkedIn requires credentials")
            error_data = response.json()
            print("Error details (shows what's required):")
            # Don't print the full error as it's very long
            print("LinkedIn requires username and password for authentication")
        elif response.status_code in [200, 201]:
            print("✅ Unexpected success - checking response...")
            data = response.json()
            print(json.dumps(data, indent=2))
        else:
            print(f"❌ Unexpected error: {response.status_code}")
            print(response.text[:500])  # First 500 chars
            
    except Exception as e:
        print(f"❌ Test Error: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 NEXT STEPS:")
    print("1. Check what error/success message you got during authentication")
    print("2. If authentication failed, verify LinkedIn credentials")
    print("3. If authentication succeeded, check Unipile dashboard manually")
    print("4. Try authentication again with debug output")
    print("\n🌐 Dashboard: https://dashboard.unipile.com")
    print("📧 Make sure you're logged into the correct Unipile account")

if __name__ == "__main__":
    debug_authentication_result()
