#!/usr/bin/env python3
"""
Test script to understand what appears in Unipile dashboard after LinkedIn authentication
"""

import requests
import json

def test_linkedin_dashboard_integration():
    """Test what data appears in dashboard after LinkedIn auth"""
    
    api_key = "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE="
    dsn = "https://api1.unipile.com:13115"
    
    print("🔍 Testing LinkedIn Dashboard Integration")
    print("=" * 60)
    
    # Check current accounts to see what's already connected
    print("\n1. Checking current accounts in dashboard...")
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'X-API-KEY': api_key,
            'accept': 'application/json'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        print(f"GET /accounts - Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("Current accounts in dashboard:")
            print(json.dumps(data, indent=2))
            
            if isinstance(data, dict) and 'items' in data:
                accounts = data['items']
                print(f"\n📊 Dashboard Summary:")
                print(f"Total accounts: {len(accounts)}")
                
                for account in accounts:
                    account_type = account.get('type', 'Unknown')
                    account_name = account.get('name', 'No name')
                    account_id = account.get('id', 'No ID')
                    sources = account.get('sources', [])
                    
                    print(f"\n🔗 {account_type} Account:")
                    print(f"  - Name: {account_name}")
                    print(f"  - ID: {account_id}")
                    print(f"  - Status: {sources[0]['status'] if sources else 'No sources'}")
                    
                    if account_type == 'LINKEDIN':
                        print(f"  ✅ This LinkedIn account should appear in your dashboard!")
                        
                        # Get more details about this LinkedIn account
                        print(f"\n2. Getting detailed LinkedIn account info...")
                        try:
                            detail_url = f"{dsn}/api/v1/accounts/{account_id}"
                            detail_response = requests.get(detail_url, headers=headers, timeout=30)
                            
                            if detail_response.status_code == 200:
                                detail_data = detail_response.json()
                                print("Detailed LinkedIn account data:")
                                print(json.dumps(detail_data, indent=2))
                            else:
                                print(f"Failed to get account details: {detail_response.status_code}")
                                
                        except Exception as e:
                            print(f"Error getting account details: {e}")
                    
                linkedin_accounts = [acc for acc in accounts if acc.get('type') == 'LINKEDIN']
                whatsapp_accounts = [acc for acc in accounts if acc.get('type') == 'WHATSAPP']
                
                print(f"\n📈 Account Types:")
                print(f"  - LinkedIn: {len(linkedin_accounts)}")
                print(f"  - WhatsApp: {len(whatsapp_accounts)}")
                print(f"  - Other: {len(accounts) - len(linkedin_accounts) - len(whatsapp_accounts)}")
                
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error checking accounts: {e}")
    
    print("\n" + "=" * 60)
    print("📋 DASHBOARD EXPECTATIONS:")
    print("After successful LinkedIn authentication, you should see:")
    print("1. ✅ New LinkedIn account in your Unipile dashboard")
    print("2. ✅ Account status showing as 'OK' or 'CONNECTED'")
    print("3. ✅ LinkedIn profile name/email as account identifier")
    print("4. ✅ Account creation timestamp")
    print("5. ✅ Available messaging sources for LinkedIn")
    print("\n🌐 Dashboard URL: https://dashboard.unipile.com")
    print("📧 Login with your Unipile account to see connected accounts")
    print("\n💡 If LinkedIn account doesn't appear:")
    print("   - Check if authentication was successful")
    print("   - Verify you're logged into the correct Unipile dashboard")
    print("   - Refresh the dashboard page")
    print("   - Check account status with: python linkedin_client.py check_status")

if __name__ == "__main__":
    test_linkedin_dashboard_integration()
