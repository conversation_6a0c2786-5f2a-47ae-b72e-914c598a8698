<!DOCTYPE html>
<html>
<head>
  <title>Connect WhatsApp with Unipile</title>
</head>
<body>
  <h2>Connect WhatsApp</h2>
  <button onclick="generateQRCode()">Generate QR</button>
  <div id="qrContainer" style="margin-top:20px;"></div>
  <div id="error" style="color: red; margin-top: 10px;"></div>

  <script>
    async function generateQRCode() {
      const apiKey = 'tSaKgy+v.JDlJ6HeFOZUGNmR62QQzk+dLFcrWdrZsbqoSH8Wgob4='; // ⚠️ Exposed!
      const dsn = "https://api13.unipile.com:14391";// Replace with your DSN

      const response = await fetch(`${dsn}/api/v1/accounts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-KEY': apiKey
        },
        body: JSON.stringify({ provider: 'WHATSAPP' })
      });

      if (!response.ok) {
        document.getElementById('error').innerText = "Failed to create WhatsApp session.";
        return;
      }

      const data = await response.json();
      const qrCodeString = data.qrCodeString;

      const qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=250x250&data=${encodeURIComponent(qrCodeString)}`;
      document.getElementById('qrContainer').innerHTML = `<img src="${qrImageUrl}" alt="Scan QR with WhatsApp" />`;
    }
  </script>
</body>
</html>
