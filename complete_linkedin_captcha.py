#!/usr/bin/env python3
"""
Script to help complete LinkedIn CAPTCHA authentication
"""

import requests
import json
import webbrowser
import sys

def complete_linkedin_captcha(account_id, captcha_solution=None):
    """Attempt to complete LinkedIn CAPTCHA authentication"""
    
    api_key = "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE="
    dsn = "https://api1.unipile.com:13115"
    
    print("🔐 LinkedIn CAPTCHA Completion Helper")
    print("=" * 60)
    print(f"Account ID: {account_id}")
    print()
    
    # First, let's check the current account status
    print("1. Checking current account status...")
    try:
        url = f"{dsn}/api/v1/accounts/{account_id}"
        headers = {
            'X-API-KEY': api_key,
            'accept': 'application/json'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("Account found! Current status:")
            print(json.dumps(data, indent=2))
            
            # Check if still in checkpoint state
            if data.get('object') == 'Checkpoint':
                checkpoint = data.get('checkpoint', {})
                if checkpoint.get('type') == 'CAPTCHA':
                    print("\n✅ Account is still in CAPTCHA checkpoint state")
                    return handle_captcha_checkpoint(account_id, checkpoint)
                else:
                    print(f"\n⚠️ Account in different checkpoint: {checkpoint.get('type')}")
            else:
                print("\n✅ Account might be fully authenticated now!")
                return True
                
        elif response.status_code == 404:
            print("❌ Account not found - it might have been removed due to failed CAPTCHA")
            return False
        else:
            print(f"❌ Error checking account: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def handle_captcha_checkpoint(account_id, checkpoint_data):
    """Handle CAPTCHA checkpoint completion"""
    
    print("\n🎯 CAPTCHA Checkpoint Details:")
    print(f"Type: {checkpoint_data.get('type')}")
    print(f"Public Key: {checkpoint_data.get('public_key')}")
    print(f"Data Length: {len(checkpoint_data.get('data', ''))}")
    
    print("\n🔧 CAPTCHA Completion Options:")
    print("1. Manual completion through Unipile dashboard")
    print("2. Browser-based authentication")
    print("3. Contact Unipile support for assistance")
    
    # Check if Unipile has a CAPTCHA completion endpoint
    print("\n2. Checking for CAPTCHA completion methods...")
    
    # Try to find documentation or endpoints for CAPTCHA completion
    api_key = "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE="
    dsn = "https://api1.unipile.com:13115"
    
    # Some APIs have a checkpoint completion endpoint
    completion_endpoints = [
        f"{dsn}/api/v1/accounts/{account_id}/checkpoint",
        f"{dsn}/api/v1/accounts/{account_id}/complete",
        f"{dsn}/api/v1/accounts/{account_id}/verify"
    ]
    
    for endpoint in completion_endpoints:
        try:
            headers = {
                'X-API-KEY': api_key,
                'accept': 'application/json'
            }
            response = requests.get(endpoint, headers=headers, timeout=10)
            if response.status_code != 404:
                print(f"✅ Found potential completion endpoint: {endpoint}")
                print(f"Status: {response.status_code}")
                if response.status_code == 200:
                    print("Response:", response.json())
        except:
            continue
    
    return False

def check_dashboard_integration():
    """Check if account appears in dashboard after CAPTCHA"""
    
    api_key = "zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE="
    dsn = "https://api1.unipile.com:13115"
    
    print("\n3. Checking dashboard integration...")
    try:
        url = f"{dsn}/api/v1/accounts"
        headers = {
            'X-API-KEY': api_key,
            'accept': 'application/json'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            data = response.json()
            accounts = data.get('items', [])
            linkedin_accounts = [acc for acc in accounts if acc.get('type') == 'LINKEDIN']
            
            print(f"Total accounts: {len(accounts)}")
            print(f"LinkedIn accounts: {len(linkedin_accounts)}")
            
            if linkedin_accounts:
                print("\n✅ LinkedIn accounts found:")
                for acc in linkedin_accounts:
                    print(f"  - ID: {acc.get('id')}")
                    print(f"  - Name: {acc.get('name')}")
                    print(f"  - Status: {acc.get('sources', [{}])[0].get('status', 'Unknown')}")
                return True
            else:
                print("❌ No LinkedIn accounts found in dashboard")
                return False
        else:
            print(f"❌ Error checking accounts: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    account_id = "9t96MFhIRuWBrJ5qJ-vRiQ"
    
    print("🚀 LinkedIn CAPTCHA Completion Process")
    print("=" * 60)
    
    # Step 1: Try to complete CAPTCHA
    success = complete_linkedin_captcha(account_id)
    
    # Step 2: Check dashboard
    dashboard_success = check_dashboard_integration()
    
    print("\n" + "=" * 60)
    print("📋 SUMMARY:")
    
    if success and dashboard_success:
        print("✅ LinkedIn account fully authenticated and visible in dashboard!")
    elif dashboard_success:
        print("✅ LinkedIn account found in dashboard (CAPTCHA might be completed)")
    else:
        print("⚠️ LinkedIn account still pending CAPTCHA completion")
        print("\n🎯 NEXT STEPS:")
        print("1. Check Unipile dashboard manually: https://dashboard.unipile.com")
        print("2. Look for pending authentications or CAPTCHA prompts")
        print("3. Contact Unipile support if no completion option available")
        print("4. Try re-authenticating with a different LinkedIn account")
    
    print("\n🌐 Unipile Dashboard: https://dashboard.unipile.com")
    print("📧 Unipile Support: Check their documentation or contact form")

if __name__ == "__main__":
    main()
