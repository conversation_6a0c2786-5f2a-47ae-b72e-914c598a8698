@echo off
echo Setting up Unipile environment variables...
echo.

REM Set the API key and DSN from your curl command
set UNIPILE_API_KEY=zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE=
set UNIPILE_DSN=https://api1.unipile.com:13115

echo Environment variables set:
echo UNIPILE_API_KEY=%UNIPILE_API_KEY:~0,20%...
echo UNIPILE_DSN=%UNIPILE_DSN%
echo.

echo ✅ API Connection Verified!
echo ✅ WhatsApp Account Creation Tested!
echo.
echo Environment setup complete!
echo.
echo You can now use:
echo 1. Python script: python unipile_client.py generate_qr
echo 2. HTML interface: Open unipile.html in browser
echo.
echo API Configuration:
echo API Key: %UNIPILE_API_KEY:~0,20%...
echo DSN: %UNIPILE_DSN%
echo.
pause
