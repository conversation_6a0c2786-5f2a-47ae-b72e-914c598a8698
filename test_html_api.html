<!DOCTYPE html>
<html>
<head>
    <title>Test Unipile API</title>
</head>
<body>
    <h2>Test Unipile API Response</h2>
    <button onclick="testAPI()">Test API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const apiKey = 'zUJiZArP.KA3XEjYnw0SwYNPY1Yk72mygJ/9GQDErUteaVxIYYnE=';
            const dsn = 'https://api1.unipile.com:13115';
            
            try {
                const response = await fetch(`${dsn}/api/v1/accounts`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-KEY': apiKey
                    },
                    body: JSON.stringify({ provider: 'WHATSAPP' })
                });

                const data = await response.json();
                console.log('Full API Response:', data);
                
                document.getElementById('result').innerHTML = `
                    <h3>API Response:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                    
                    <h3>QR Code Analysis:</h3>
                    <p>Object type: ${data.object}</p>
                    <p>Account ID: ${data.account_id}</p>
                    ${data.checkpoint ? `
                        <p>Checkpoint type: ${data.checkpoint.type}</p>
                        <p>QR Code found: ${data.checkpoint.qrcode ? 'YES' : 'NO'}</p>
                        ${data.checkpoint.qrcode ? `
                            <p>QR Code: ${data.checkpoint.qrcode.substring(0, 50)}...</p>
                            <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(data.checkpoint.qrcode)}" alt="QR Code" />
                        ` : ''}
                    ` : '<p>No checkpoint found</p>'}
                `;
                
            } catch (error) {
                document.getElementById('result').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
                console.error('Error:', error);
            }
        }
    </script>
</body>
</html>
